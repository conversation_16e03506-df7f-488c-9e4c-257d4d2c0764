repos:
  - repo: local
    hooks:
      # Black: Code formatter
      - id: black
        name: black
        entry: poetry run black
        language: python
        types: [python]
        args: ["--line-length", "120"]

      # isort: Imports sorting
      - id: isort
        name: isort
        entry: poetry run isort
        language: python
        types: [python]
        args:
          - "--profile"
          - "black"
          - "--line-length"
          - "120"
          - "--force-sort-within-sections"

      # autoflake: Remove unused imports and variables
      - id: autoflake
        name: autoflake
        entry: poetry run autoflake
        language: python
        types: [python]
        args:
          - "--in-place"
          - "--remove-all-unused-imports"
          - "--remove-unused-variables"
          - "--recursive"

      # flake8: Linting
      - id: flake8
        name: flake8
        entry: poetry run flake8
        language: python
        types: [python]
        args:
          - "--ignore=E501,E203"
          - "--max-line-length=120"

      # mypy: Type checking
      # - id: mypy
      #   name: mypy
      #   entry: poetry run mypy
      #   language: python
      #   types: [python]
      #   require_serial: true