import os

from azure.storage.blob import BlobServiceClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def upload_file_to_blob(file_path, blob_name):
    """
    Upload a file to Azure Blob Storage

    Args:
        file_path (str): Local path to the file to upload
        blob_name (str): Name/path for the blob in the container

    Returns:
        bool: True if upload successful, False otherwise
    """
    try:
        # Get environment variables
        account_name = os.getenv("BLOB_ACCOUNT_NAME")
        account_key = os.getenv("BLOB_ACCOUNT_KEY")
        container_name = os.getenv("BLOB_CONTAINER_NAME")

        blob_service_client = BlobServiceClient.from_connection_string(
            conn_str=f"DefaultEndpointsProtocol=https;AccountName={account_name};AccountKey={account_key};EndpointSuffix=core.windows.net"
        )

        # Get blob client
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        # Upload file
        print(f"Uploading {file_path} to {blob_name}...")
        with open(file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)

        print(f"Successfully uploaded {file_path} to blob: {blob_name}")
        return True

    except Exception as e:
        print(f"Error uploading file: {str(e)}")
        return False


def main():
    import argparse

    parser = argparse.ArgumentParser(description="Upload file to Azure Blob Storage")
    parser.add_argument("local_file_path", help="Path to the local file to upload")
    parser.add_argument("blob_name", help="Name/path for the blob in the container")

    args = parser.parse_args()

    # Example usage
    local_file_path = args.local_file_path
    blob_name = args.blob_name

    # Upload file
    success = upload_file_to_blob(local_file_path, blob_name)

    if success:
        print("Upload completed successfully!")
    else:
        print("Upload failed!")


if __name__ == "__main__":
    main()
