# User Requirement Specification

**Project Name:** Leave Request Management System  
**Prepared for:** Bidding Vendors  
**Prepared by:** [Client Business Team]  
**Version:** 1.0  
**Date:** May 09, 2025  

---

## 1. Introduction

This document outlines the key user requirements for a Leave Request Management System. The purpose of the system is to manage the full lifecycle of employee leave requests — from submission to approval — while ensuring compliance with company policy and improving transparency.

---

## 2. Business Context

Employees are entitled to various types of leave based on their seniority, gender, and company policy. However, the current process for leave request and approval is manual, lacks consistency, and is prone to delays. This new system should automate and standardize the leave process while integrating with existing time tracking systems (e.g., card scanners for check-in/out).

---

## 3. Objectives

- Automate the leave request workflow across all levels.
- Ensure rule-based validation and policy transparency.
- Support differentiated roles (employee, project manager, manager).
- Send regular notifications to ensure compliance.
- Allow flexible leave submission even when violating policy (with manual review).
- Improve visibility into absenteeism without approved leave.

---

## 4. Scope of Requirements

### 4.1 Leave Request Submission

Employees can submit leave requests for future dates or past dates within the current month only. Leave types include Paid Leave (based on seniority), Maternity Leave (for female employees), and other types. Requests that violate policy are allowed but subject to review. Leave data is refreshed annually.

### 4.2 Review & Approval Workflow

- **Step 1: Confirmation** – Project Manager confirms or denies leave within the same month of leave start date. Project Managers submitting for themselves are auto-confirmed.
- **Step 2: Approval** – Line Manager reviews and approves/rejects the confirmed request within the same month.
- Employees can modify and resubmit requests after denial or rejection.

### 4.3 Attendance-based Notification (Weekly)

Every Friday, the system notifies employees about:
- Days they were absent without a leave request
- Days they were absent but had a valid leave request

A day is considered 'off work' if:
- No check-in time recorded
- **Or** total presence on that day is less than 4 hours

### 4.4 Leave Entitlement Import

At the beginning of each year, HR/Admin can import leave entitlements for employees. Entitlements vary based on seniority and gender.

### 4.5 Leave Type Management

The system must support management of different types of leave, each with its own eligibility rules and documentation requirements.

- Leave types can differ by gender. For example, maternity leave is only available to female employees.
- Some types of leave, such as Paid Leave, vary by years of service (seniority) based on government regulations.
- Each leave type may define required documentation. For example, 'Medical Leave with Hospital Certificate' may require employees to upload a hospital document as defined in system settings.
- Admins must be able to configure these rules and document requirements for each leave type.

### 4.6 Role Definitions

- **Employee:** Can submit leave requests and view status
- **Project Manager:** Confirms leave requests of project team members
- **Line Manager:** Approves or rejects confirmed requests
- **Admin/HR:** Manages entitlement data and annual refresh

### 4.7 Screen and Reporting Requirements

The system must provide separate, role-specific screens to enhance user experience and approval efficiency.

- Project Managers and Line Managers will have dedicated dashboards where only the leave requests requiring their action are shown by default.
- The views must include filters for request type, employee name, date, and request status (e.g., pending, approved, denied).
- HR and Managers will have access to reporting screens to view leave statistics.
- Reports should include leave usage summarized by unit, department, or the entire company.
- **Suggested leave types to support include:**
    - Paid Leave
    - Unpaid Leave
    - Maternity Leave
    - Paternity Leave
    - Sick Leave (with Hospital Certificate)
    - Bereavement Leave
    - Study Leave
    - Personal Leave

### 4.8 Integration Requirements

Integrate with existing card scanner system to fetch check-in/check-out times. Processing logic of scanner data is out of scope.

The company is a multinational corporation with offices located in multiple cities across several countries. The system must support organizational structures that reflect this geographical complexity, enabling role-based access and reporting by country, city, or office. It must scale to support up to 33,000 users, ensuring performance and reliability under heavy usage.

---

## 5. Non-Functional Requirements

- The system should be web-based and mobile-responsive
- Must support secure login/logout for all user roles
- Should handle concurrent submissions without delay
- Notification emails must be automated and configurable
- Access rights should be role-based

---

## 6. Assumptions and Constraints

- All review and approval actions must be completed within the calendar month of the leave start date
- System should be localized (multi-language) if needed
- The vendor is not required to implement card scanner logic, only use data provided

---

## 7. Deliverables from Vendor

- Technical Proposal including system architecture
- Functional Prototype (if possible)
- Implementation Plan with timeline
- Training Plan and Documentation
- Post-deployment Support Plan