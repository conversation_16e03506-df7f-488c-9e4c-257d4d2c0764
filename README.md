# 🚀 Quick Start
 
This guide shows how to run the FastAPI service and seed MongoDB using Docker Compose.
 
---
 
## 1️ Run the Application
 
```bash
docker compose up --build
```

## 2 Adding sample data into mongodb

### Set up package management

#### Install uv and update dependencies
```bash
pip install uv
uv sync
```

#### Add data into mongdodb
Create new terminal or (Ctrl + Shift + 5) and run this command

```bash
uv run python scripts/seed.py
```