FROM agnohq/python:3.12

ARG USER=app
ARG APP_DIR=/app
ENV APP_DIR=${APP_DIR}

# Create user and working directory
RUN groupadd -g 61000 ${USER} && \
  useradd -u 61000 -g 61000 -ms /bin/bash -d ${APP_DIR} ${USER}

WORKDIR ${APP_DIR}

# Install uv
RUN pip install uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Copy app code
COPY . .

# Create NLTK data directory with proper permissions
RUN mkdir -p ${APP_DIR}/nltk_data

# Make sure app folder is owned by the non-root user
RUN chown -R ${USER}:${USER} ${APP_DIR}

# Switch to non-root user
USER ${USER}

# Run uv sync normally (now safe because /app/.cache/uv is mounted separately)
# Run uv sync with SSL handling for restricted networks
RUN UV_INSECURE_HOST=github.com uv sync || \
  UV_NO_VERIFY_SSL=1 uv sync

# EXPOSE 8000

CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]