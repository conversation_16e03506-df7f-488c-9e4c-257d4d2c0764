#!/usr/bin/env python3
"""
Migration script to add status field to existing agents

This script adds the status field with default value DRAFT (0) to all agents
that don't already have the status field.

Usage:
    docker compose exec fastapi-app uv run python add_agent_status_migration.py
"""

import asyncio
import os
import sys
from motor.motor_asyncio import AsyncIOMotorClient


async def migrate_agent_status():
    """Add status field to existing agents"""
    # MongoDB connection settings
    mongodb_url = os.getenv("MONGODB_URL", "mongodb://mongodb:27017")
    database_name = os.getenv("DATABASE_NAME", "bavista")
    
    print(f"🔌 Connecting to MongoDB: {mongodb_url}")
    print(f"📊 Database: {database_name}")
    
    # Create MongoDB client
    client = AsyncIOMotorClient(mongodb_url)
    db = client[database_name]
    agents_collection = db.agents
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✅ MongoDB connection successful")
        
        # Count agents without status field
        agents_without_status = await agents_collection.count_documents({"status": {"$exists": False}})
        
        print(f"📊 Found {agents_without_status} agents without status field")
        
        if agents_without_status > 0:
            # Add status field with default value DRAFT (0)
            result = await agents_collection.update_many(
                {"status": {"$exists": False}},
                {"$set": {"status": 0}}  # AgentStatus.DRAFT = 0
            )
            
            print(f"✅ Updated {result.modified_count} agents with status='draft' (0)")
        else:
            print("ℹ️  All agents already have status field - no migration needed")
        
        # Verify the migration
        total_agents = await agents_collection.count_documents({})
        agents_with_status = await agents_collection.count_documents({"status": {"$exists": True}})
        
        print(f"\n📋 Migration verification:")
        print(f"  - Total agents: {total_agents}")
        print(f"  - Agents with status: {agents_with_status}")
        
        if total_agents == agents_with_status:
            print("✅ Migration completed successfully!")
        else:
            print("❌ Migration incomplete - some agents still missing status field")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return False
    finally:
        # Close the connection
        client.close()
        print("🔌 MongoDB connection closed")


async def main():
    """Main function to run the migration"""
    print("🚀 Starting Agent Status Migration")
    print("=" * 50)
    
    success = await migrate_agent_status()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("\n📝 Status values:")
        print("   0 = Draft")
        print("   1 = Submitted") 
        print("   7 = Approved")
        sys.exit(0)
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
