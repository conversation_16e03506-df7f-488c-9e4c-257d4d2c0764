# Leave Request Type Management Screen Details

## Screen Information
- **Description:** Administrative screen for HR to manage leave types including creation, modification, and deactivation of leave types with full CRUD operations
- **Access:** Triggered when HR Administrator selects "Leave Type Management" from admin menu or Line Manager accesses for read-only view

## Screen Description Table

| No. | Component | Comp.Type | Editable (Y/N) | Mandatory (Y/N) | Default Value | Description |
|-----|-----------|-----------|----------------|-----------------|---------------|-------------|
| 1 | Page Title | Label | N | N | "Leave Request Type Management" | Static header displaying the screen title |
| 2 | Add New Type Button | Button | N | N | - | On-click opens "Add Leave Type" modal dialog. Visible only to HR Administrator role |
| 3 | Search Box | Text box | Y | N | - | Free text search to filter leave types by Type Name or Type Code. Placeholder: "Search by type name or code" |
| 4 | Status Filter | Single choice dropdown list | Y | N | "All Status" | Dropdown with options: All Status, Active, Inactive. Filters the leave type list |
| 5 | Search Button | Button | N | N | - | On-click triggers search and filter application to refresh the table |
| 6 | Reset Filter Button | Button | N | N | - | On-click clears search box and status filter, reloads all leave types |
| 7 | Type Code Column | Label | N | N | - | Display unique identifier code for leave type. Links to LeaveType.typeCode. Sortable column |
| 8 | Type Name Column | Label | N | N | - | Display name of the leave type. Links to LeaveType.typeName. Sortable column |
| 9 | Description Column | Label | N | N | - | Brief description of the leave type. Links to LeaveType.description. Truncated with "..." if too long |
| 10 | Max Days Allowed Column | Label | N | N | - | Maximum number of days allowed for this leave type. Links to LeaveType.maxDaysAllowed. Display "Unlimited" if null |
| 11 | Gender Restriction Column | Label | N | N | - | Shows gender restriction if any. Links to LeaveType.genderRestriction. Display "All", "Male Only", "Female Only" |
| 12 | Document Required Column | Label | N | N | - | Indicates if documentation is required. Links to LeaveType.documentRequired. Display "Yes/No" with icons |
| 13 | Status Column | Label | N | N | - | Current status with color coding: Green (Active), Red (Inactive). Links to LeaveType.isActive |
| 14 | Created Date Column | Label | N | N | - | Date when leave type was created. Links to LeaveType.createdDate. Format: DD/MM/YYYY |
| 15 | Action Column | Button | N | N | - | Contains Edit and Delete buttons. Edit: visible to HR only. Delete: visible to HR only for inactive types |
| 16 | Records Per Page | Single choice dropdown list | Y | N | 20 | Dropdown with options: 10, 20, 50, 100 records per page |
| 17 | Pagination Controls | Button | N | N | - | Previous, Next, and page number buttons for navigation through multiple pages |
| 18 | Total Records Label | Label | N | N | - | Display total count: "Showing X to Y of Z entries" |
| 19 | Edit Modal Dialog | Modal | Y | N | - | Pop-up dialog for editing leave type details. Contains form fields for all editable properties |
| 20 | Add Modal Dialog | Modal | Y | N | - | Pop-up dialog for creating new leave type. Contains form fields with validation |
| 21 | Delete Confirmation Dialog | Modal | N | N | - | Confirmation dialog: "Are you sure you want to delete this leave type? This action cannot be undone." |
| 22 | Type Code Field (Modal) | Text box | Y | Y | - | Input field for leave type code. Max 10 characters, alphanumeric only. Required and unique validation |
| 23 | Type Name Field (Modal) | Text box | Y | Y | - | Input field for leave type name. Max 100 characters. Required field with trimming |
| 24 | Description Field (Modal) | Text area | Y | N | - | Multi-line input for description. Max 500 characters. Optional field |
| 25 | Max Days Allowed Field (Modal) | Number | Y | N | - | Numeric input for maximum days. Range 1-365 or empty for unlimited. Validation for positive numbers |
| 26 | Gender Restriction Field (Modal) | Single choice dropdown list | Y | N | "All" | Dropdown with options: All, Male Only, Female Only. Links to gender validation rules |
| 27 | Document Required Field (Modal) | Checkbox | Y | N | Unchecked | Checkbox to indicate if supporting documents are required for this leave type |
| 28 | Active Status Field (Modal) | Checkbox | Y | N | Checked | Checkbox to set leave type as active/inactive. Checked = Active |
| 29 | Save Button (Modal) | Button | N | N | - | On-click validates form and saves leave type data. Closes modal on success |
| 30 | Cancel Button (Modal) | Button | N | N | - | On-click closes modal without saving changes. Shows confirmation if data was modified |