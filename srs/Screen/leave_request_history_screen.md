# Leave Request History Screen Details

## Screen Information

**Description:** This screen displays a comprehensive list of all leave requests submitted by the user (for employees) or team members (for managers/HR). It allows users to track the status of requests, view historical data, and take actions on denied requests.

**Access:** 
- Triggered when user clicks on "Leave History" or "My Requests" from the main navigation menu
- Accessible to all authenticated users based on their role permissions
- Employees see only their own requests
- Project Managers see their team's requests
- Line Managers see requests requiring their approval plus their team history
- HR/Admin see all requests across the organization

## Screen Description Table

| No. | Component | Comp.Type | Editable (Y/N) | Mandatory (Y/N) | Default Value | Description |
|-----|-----------|-----------|----------------|-----------------|---------------|-------------|
| 1 | Page Title | Label | N | N | "Leave Request History" | Static header displaying the screen title |
| 2 | Date Range Filter | Date time (Date only) | Y | N | Last 12 months | Start and end date fields to filter requests by submission date range. Format: DD/MM/YYYY |
| 3 | Leave Type Filter | Single choice dropdown list | Y | N | "All Types" | Dropdown containing all available leave types plus "All Types" option. Links to LeaveType data object |
| 4 | Status Filter | Multiple choices dropdown list | Y | N | "All Status" | Multiple dropdown with options: All Status, Draft, Submitted, Confirmed, Approved, Denied, Rejected, Cancelled, Pending |
| 4a | Special Requests Only Filter | Check box | Y | N | Unchecked | Checkbox to show only special leave requests. When checked, highlights and filters special requests |
| 5 | Search Button | Button | N | N | - | On-click triggers filter application and table refresh with filtered results |
| 6 | Reset Filter Button | Button | N | N | - | On-click clears all filter selections and reloads default view |
| 7 | Export Button | Button | N | N | - | On-click exports filtered results to Excel/PDF format. Visible only to managers and HR |
| 8 | Request ID Column | Label | N | N | - | Display unique identifier for each leave request. Links to LeaveRequest.requestId |
| 9 | Employee Name Column | Label | N | N | - | Shows employee name. For employee role, shows "You". Links to Employee.name data object |
| 10 | Special leave Type Column | Label | N | N | - | Displays the type of leave requested with special highlighting for special leave types (golden border/background). Links to LeaveType.name |
| 11 | Start Date Column | Label | N | N | - | Shows leave start date in DD/MM/YYYY format. Links to LeaveRequest.startDate |
| 12 | End Date Column | Label | N | N | - | Shows leave end date in DD/MM/YYYY format. Links to LeaveRequest.endDate |
| 13 | Duration Column | Label | N | N | - | Calculated field showing total leave days. Format: "X days" |
| 14 | Status Column | Label | N | N | - | Current status with color coding: Green (Approved), Red (Denied/Rejected), Orange (Pending), Blue (Draft) |
| 15 | Submission Date Column | Label | N | N | - | Date when request was submitted. Format: DD/MM/YYYY HH:MM |
| 16 | Action Column | Button | N | N | - | Contains action buttons based on status: View Details (all), Edit (Draft), Resubmit (Denied/Rejected) |
| 17 | Document Link | Link | N | N | - | Shows "View Documents" link if request has attachments. On-click opens document viewer |
| 18 | View Details Button | Button | N | N | - | On-click opens request detail popup/page showing full request information including special leave request details |
| 18a | Special Leave Indicator | Icon | N | N | - | Star or special icon displayed for special leave requests. Visible in the row to highlight special requests |
| 19 | Edit Button | Button | N | N | - | Visible only for Draft status. On-click navigates to edit request form |
| 20 | Resubmit Button | Button | N | N | - | Visible only for Denied/Rejected status. On-click allows modification and resubmission |
| 21 | Pagination Controls | Component Group | Y | N | 20 records per page | Previous/Next buttons and page numbers for navigating through multiple pages of results |
| 22 | Records Per Page | Single choice dropdown list | Y | N | 20 | Options: 10, 20, 50, 100 records per page |
| 23 | Total Records Label | Label | N | N | - | Displays "Showing X-Y of Z records" for user reference |
| 24 | Sort Headers | Button | N | N | Submission Date (DESC) | Clickable column headers allowing sorting by Request ID, Start Date, End Date, Submission Date, Status |
| 25 | No Data Message | Label | N | N | - | Displays "No leave requests found" when no records match the filter criteria |
| 26 | Loading Indicator | Icon | N | N | - | Shows spinner/loading animation while data is being fetched |
| 27 | Refresh Button | Button | N | N | - | On-click reloads the current view with latest data from server |
| 28 | Special Request Row Highlighting | Component Group | N | N | - | Table rows for special leave requests have distinct visual styling: golden/yellow background or border to make them stand out |
| 29 | Special Leave Details Modal | Modal | N | N | - | Enhanced detail view specifically for special leave requests showing additional information like special approval requirements, documentation, and business justification |
