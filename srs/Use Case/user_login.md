# User Login Use Case

## UC Info Table

| Item | Description |
|------|-------------|
| Objective | Allow users to authenticate and gain access to the Leave Request Management System based on their assigned roles |
| Actor | Employee, Project Manager, Line Manager, HR Administrator |
| Precondition | User has valid credentials (username/password) and system is accessible |
| Trigger | User navigates to the system URL or clicks "Login" button |
| Post-condition | User is authenticated and redirected to their role-specific dashboard with appropriate permissions |

## Activity Flow Table

| Step | User | System |
|------|------|--------|
| 1 | User navigates to system URL | Display Login Screen with username/password fields |
| 2 | User enters username in username field | Validate field format and enable login button when both fields have values |
| 3 | User enters password in password field | Mask password characters for security |
| 4 | User clicks Login button | Validate credentials against user database |
| 4.1 | - | If credentials are invalid, display "Invalid username or password" error message and remain on login screen |
| 4.2 | - | If account is locked/disabled, display "Account is locked. Please contact administrator" error message |
| 4.3 | - | If credentials are valid, create user session and load user profile with role information |
| 5 | - | Redirect user to appropriate dashboard based on their role |
| 5.1 | - | If Employee role, redirect to Employee Dashboard |
| 5.2 | - | If Project Manager role, redirect to Project Manager Dashboard |
| 5.3 | - | If Line Manager role, redirect to Line Manager Dashboard |
| 5.4 | - | If HR Administrator role, redirect to HR Administrator Dashboard |
| 6 | User clicks "Forgot Password" link (optional) | Navigate to password reset screen |
| 7 | User clicks "Remember Me" checkbox (optional) | Store login credentials securely for future sessions |

## Business Rules Table

| Step | Description |
|------|-------------|
| Step 1 | **Login Screen Display**<br/>- Display "Leave Request Management System" branding<br/>- Show username field with placeholder "Enter your username"<br/>- Show password field with placeholder "Enter your password"<br/>- Display "Remember Me" checkbox<br/>- Display "Forgot Password?" link<br/>- Login button initially disabled until both fields have values |
| Step 2-3 | **Input Field Validation**<br/>- Username field: Required, minimum 3 characters, no special characters except underscore and dot<br/>- Password field: Required, minimum 8 characters<br/>- Show inline validation messages for invalid input<br/>- Enable Login button only when both fields meet minimum requirements |
| Step 4 | **Authentication Process**<br/>- Hash password using secure algorithm before comparison<br/>- Check credentials against "User" object in database<br/>- Log authentication attempt with timestamp, IP address, and result<br/>- Implement rate limiting: maximum 5 failed attempts per IP address per 15 minutes |
| Step 4.1 | **Invalid Credentials Handling**<br/>- Increment failed login counter for the user account<br/>- Display generic error message to prevent username enumeration<br/>- Log failed attempt with username and IP address<br/>- Clear password field and maintain username for user convenience |
| Step 4.2 | **Account Lock Handling**<br/>- Check if user account [Status] = "Locked" or "Disabled"<br/>- Display appropriate error message<br/>- Log locked account access attempt<br/>- Prevent further authentication attempts |
| Step 4.3 | **Successful Authentication**<br/>- Create secure session token with expiration time<br/>- Load user profile including [Role], [Permissions], [OrganizationalUnit]<br/>- Reset failed login counter to 0<br/>- Update [LastLoginDate] and [LastLoginIP] in "User" object<br/>- Log successful login event |
| Step 5 | **Role-based Redirection**<br/>- Determine target dashboard based on user [Role] from "User" object<br/>- Load role-specific menu and navigation options<br/>- Apply role-based access controls and permissions<br/>- Set session timeout based on role (Employee: 8 hours, Managers: 12 hours, HR: 24 hours) |
| Step 6 | **Forgot Password Flow**<br/>- Navigate to password reset screen<br/>- Maintain security by not revealing whether username exists<br/>- Send "Password Reset Request" email if username is valid |
| Step 7 | **Remember Me Functionality**<br/>- If checked, store encrypted credentials in secure browser storage<br/>- Set extended session timeout (30 days)<br/>- Apply additional security validations for extended sessions |

## Additional Output Components

### Message Table

| Category | Message |
|----------|---------|
| Error Message | Invalid username or password. Please try again. |
| Error Message | Account is locked. Please contact administrator for assistance. |
| Error Message | Username is required. Please enter your username. |
| Error Message | Password is required. Please enter your password. |
| Error Message | Username must be at least 3 characters long. |
| Error Message | Password must be at least 8 characters long. |
| Error Message | Too many failed login attempts. Please try again in 15 minutes. |
| Error Message | System is temporarily unavailable. Please try again later. |
| Success Dialog | Login successful. Redirecting to your dashboard... |
| Confirmation Dialog | Do you want the system to remember your login credentials? |

### Email Template

| Objective | Notify user about password reset request and provide secure reset link |
|-----------|---------|
| Send to | User's registered email address |
| CC | - |
| Subject | Password Reset Request - Leave Request Management System |
| Body | Dear {User Name},<br/><br/>We received a request to reset your password for the Leave Request Management System.<br/><br/>If you requested this password reset, please click the link below to create a new password:<br/>{Password Reset Link}<br/><br/>This link will expire in 24 hours for security reasons.<br/><br/>If you did not request this password reset, please ignore this email and contact our IT support team immediately.<br/><br/>Best regards,<br/>IT Support Team<br/>Leave Request Management System |
| Remarks | Password reset link should be generated with unique token and 24-hour expiration |

### Common Business Rule Table

| CBR Name | Description |
|----------|-------------|
| Audit Logging | All login attempts (successful and failed) are logged with timestamp, user ID, IP address, and result for security monitoring and compliance purposes |
| Session Management | User sessions are managed with secure tokens, appropriate timeouts, and automatic cleanup of expired sessions |
| Security Rate Limiting | Implement rate limiting across the system to prevent brute force attacks and ensure system availability |

### Use Case Question Table
*No clarification questions - all requirements are clear from the security and authentication requirements.*
