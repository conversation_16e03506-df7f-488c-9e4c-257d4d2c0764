# View Leave Request History Use Case

## UC Info Table

| Item | Description |
|------|-------------|
| Objective | Allow users to view and manage their leave request history with filtering, sorting, and action capabilities based on their role permissions |
| Actor | Employee, Project Manager, Line Manager, HR Administrator |
| Precondition | User is logged in and has appropriate role permissions |
| Trigger | User clicks on "Leave History" or "My Requests" from the main navigation menu |
| Post-condition | Leave request history is displayed with appropriate data based on user role and applied filters |

## Activity Flow Table

| Step | User | System |
|------|------|--------|
| 1 | User clicks on "Leave History" or "My Requests" from navigation menu | Navigate to Leave Request History Screen |
| 2 | - | Load and display leave request data based on user role with default filters (last 12 months, all types, all status) |
| 3 | User optionally sets date range filter | Update filter criteria |
| 4 | User optionally selects leave type from dropdown | Update filter criteria |
| 5 | User optionally selects status from dropdown | Update filter criteria |
| 6 | User clicks Search button | Apply filters and refresh table data |
| 6.1 | - | If no records found, display "No leave requests found" message |
| 6.2 | - | If records found, display filtered results with pagination |
| 7 | User clicks on column header to sort | Sort data by selected column and refresh display |
| 8 | User clicks View Details button for a specific request | Open request detail popup/page with full information |
| 9 | User clicks Edit button (only visible for Draft status) | Navigate to edit request form |
| 10 | User clicks Resubmit button (only visible for Denied/Rejected status) | Navigate to request modification form |
| 11 | User clicks Document link (if available) | Open document viewer showing attached files |
| 12 | User clicks Export button (managers/HR only) | Generate and download Excel/PDF report of filtered results |
| 13 | User clicks Reset Filter button | Clear all filters and reload default view |
| 14 | User changes records per page setting | Refresh display with new pagination setting |
| 15 | User navigates through pages using pagination controls | Load and display selected page data |

## Business Rules Table

| Step | Description |
|------|-------------|
| Step 2 | **Role-based Data Access**<br/>- Employees: Load only requests where [SubmittedBy] = {current login user}<br/>- Project Managers: Load requests where [SubmittedBy] in {team members} OR [SubmittedBy] = {current login user}<br/>- Line Managers: Load requests where [RequiresApproval] = {current login user} OR requests from their reporting hierarchy<br/>- HR Administrator: Load all "Leave Request" records<br/>Apply default sorting by [SubmissionDate] DESC |
| Step 3-5 | **Filter Validation**<br/>- Date range: [StartDate] must be <= [EndDate]<br/>- If invalid date range, show "Invalid date range" error message<br/>- Store filter criteria in session for user experience continuity |
| Step 6 | **Filter Application Logic**<br/>- Apply date filter: [SubmissionDate] between {selected start date} and {selected end date}<br/>- Apply leave type filter: [LeaveTypeId] = {selected leave type} (if not "All Types")<br/>- Apply status filter: [Status] = {selected status} (if not "All Status")<br/>- Combine filters with AND logic<br/>- Apply pagination with default 20 records per page |
| Step 6.1 | **No Data Handling**<br/>- Display "It seem be that you have not yet had any leave requests." message when result set is empty<br/>- Hide pagination controls<br/>- Maintain filter settings |
| Step 7 | **Sorting Logic**<br/>- Support sorting by [RequestId], [StartDate], [EndDate], [SubmissionDate], [Status]<br/>- Toggle between ASC/DESC on repeated clicks<br/>- Maintain current filters and pagination settings |
| Step 8 | **View Details Authorization**<br/>- Verify user has permission to view selected "Leave Request"<br/>- Load complete request data including approval history<br/>- Display in popup or navigate to detail page |
| Step 9 | **Edit Button Visibility**<br/>- Show Edit button only when [Status] = "Draft" AND [SubmittedBy] = {current login user}<br/>- Navigate to "Leave Request Form Screen" with pre-populated data |
| Step 10 | **Resubmit Button Visibility**<br/>- Show Resubmit button only when [Status] in ("Denied", "Rejected") AND [SubmittedBy] = {current login user}<br/>- Navigate to "Leave Request Form Screen" with modification capabilities |
| Step 11 | **Document Access Control**<br/>- Show Document link only when "Leave Request".[HasAttachments] = true<br/>- Verify user has permission to view documents<br/>- Open document viewer with file list |
| Step 12 | **Export Authorization**<br/>- Show Export button only for Project Manager, Line Manager, and HR Administrator roles<br/>- Generate report including filtered data with columns: Request ID, Employee Name, Leave Type, Start Date, End Date, Duration, Status, Submission Date<br/>- Support Excel and PDF formats |
| Step 13 | **Reset Filter Logic**<br/>- Clear all filter selections to default values<br/>- Reload data with default filters (last 12 months, all types, all status)<br/>- Reset pagination to first page |
| Step 14-15 | **Pagination Management**<br/>- Maintain filter and sort settings during pagination<br/>- Update URL parameters for bookmarking capability<br/>- Display total record count and current page information |

## Additional Output Components

### Message Table

| Category | Message |
|----------|---------|
| Error Message | Invalid date range. Please ensure start date is before or equal to end date. |
| Error Message | Unable to load leave request data. Please try again later. |
| Error Message | You do not have permission to view this request. |
| Success Dialog | Export completed successfully. Your file is ready for download. |
| Confirmation Dialog | Are you sure you want to reset all filters? This will clear your current search criteria. |

### Email Template
*No email templates required for this use case.*

### Common Business Rule Table

| CBR Name | Description |
|----------|-------------|
| Audit Logging | All user actions (view, filter, export) are logged with timestamp, user ID, and action details for compliance and monitoring purposes |
| Session Management | User filter preferences and pagination settings are maintained in session storage to improve user experience across page refreshes |
| Data Pagination | Implement consistent pagination behavior across all list screens with configurable page sizes and navigation controls |

### Use Case Question Table
*No clarification questions - all requirements are clear from the user requirements and screen specifications.*

### Business Process Overview
This use case is part of the larger Leave Request Management workflow, serving as a central hub for users to monitor their leave request lifecycle. It integrates with:
- Submit Leave Request process (via Edit/Resubmit actions)
- Approval Workflow process (status tracking)
- Document Management system (attachment viewing)
- Reporting and Analytics (export functionality)
