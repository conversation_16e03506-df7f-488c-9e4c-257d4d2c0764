# Actor List - Leave Request Management System

| Actor Name | Description | Role in System | Assumption | Question |
|------------|-------------|----------------|------------|----------|
| Employee | Regular staff member who needs to request time off from work | Submit leave requests, view request status, modify rejected requests, receive attendance notifications | All employees have access to the system and understand leave policies | - |
| Project Manager | Manager responsible for overseeing project teams and their work assignments | Confirm or deny leave requests from team members within the same month, auto-confirmed for own requests | Project Managers have clear authority over team member leave decisions and understand project impact | What happens if a Project Manager is unavailable to confirm requests within the monthly deadline? |
| Line Manager | Senior manager with authority to make final approval decisions on leave requests | Approve or reject confirmed leave requests, access reporting dashboards, view team leave statistics | Line Managers have final authority on leave decisions and understand business impact | How are Line Manager assignments determined for employees who work across multiple departments? |
| HR Administrator | Human Resources staff responsible for managing employee data and leave policies | Import annual leave entitlements, manage leave types and rules, configure system settings, access comprehensive reports | HR has authority to modify system configurations and access to all employee leave data | What level of technical access should HR have for system configuration? |
| System Administrator | IT staff responsible for maintaining the technical aspects of the system | Manage user accounts, system maintenance, integration with card scanner system, technical troubleshooting | System Admin has full technical access but limited business rule modification rights | Should System Administrators have read-only access to business data or full access for troubleshooting? |
| Card Scanner System | Automated system that tracks employee check-in/check-out times | Provide attendance data to the leave management system for absence tracking and notifications | The card scanner system has reliable data and established integration capabilities | What format does the card scanner system provide data in, and how frequently is it updated? |
