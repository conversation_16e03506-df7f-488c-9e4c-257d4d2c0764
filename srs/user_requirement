# User Requirements

## 1. Leave Request Submission
**Name:** Leave Request Submission  
**Description:** Employees can submit leave requests for future dates or past dates within the current month only. The system should support various leave types including Paid Leave (based on seniority), Maternity Leave (for female employees), and other types. Requests that violate policy are allowed but subject to manual review.

## 2. Two-Step Approval Workflow
**Name:** Two-Step Approval Workflow  
**Description:** Implement a two-step approval process where Step 1 involves Project Manager confirmation or denial within the same month of leave start date, and Step 2 involves Line Manager approval or rejection of confirmed requests. Project Managers submitting for themselves are auto-confirmed.

## 3. Request Modification After Denial
**Name:** Request Modification After Denial  
**Description:** Employees can modify and resubmit leave requests after they have been denied by Project Manager or rejected by Line Manager.

## 4. Weekly Attendance Notification
**Name:** Weekly Attendance Notification  
**Description:** Every Friday, the system notifies employees about days they were absent without a leave request and days they were absent but had a valid leave request. A day is considered 'off work' if no check-in time is recorded or total presence is less than 4 hours.

## 5. Annual Leave Entitlement Import
**Name:** Annual Leave Entitlement Import  
**Description:** At the beginning of each year, HR/Admin can import leave entitlements for employees. Entitlements vary based on seniority and gender, and leave data is refreshed annually.

## 6. Leave Type Management
**Name:** Leave Type Management  
**Description:** The system must support management of different types of leave with their own eligibility rules and documentation requirements. Leave types can differ by gender and seniority, and each type may define required documentation that employees must upload.

## 7. Role-Based Access Control
**Name:** Role-Based Access Control  
**Description:** The system must support differentiated roles including Employee (submit requests and view status), Project Manager (confirm team member requests), Line Manager (approve/reject confirmed requests), and Admin/HR (manage entitlement data).

## 8. Dedicated Role-Specific Dashboards
**Name:** Dedicated Role-Specific Dashboards  
**Description:** Project Managers and Line Managers will have dedicated dashboards showing only leave requests requiring their action by default, with filters for request type, employee name, date, and request status.

## 9. Leave Reporting and Statistics
**Name:** Leave Reporting and Statistics  
**Description:** HR and Managers will have access to reporting screens to view leave statistics including leave usage summarized by unit, department, or entire company.

## 10. Card Scanner Integration
**Name:** Card Scanner Integration  
**Description:** Integrate with existing card scanner system to fetch check-in/check-out times for attendance tracking purposes. The system will use the data provided without implementing the scanner logic itself.

## 11. Multi-Location Organizational Support
**Name:** Multi-Location Organizational Support  
**Description:** The system must support organizational structures reflecting geographical complexity across multiple countries and cities, enabling role-based access and reporting by country, city, or office location.

## 12. Large-Scale User Support
**Name:** Large-Scale User Support  
**Description:** The system must scale to support up to 33,000 users, ensuring performance and reliability under heavy concurrent usage.

## 13. Web-Based Mobile-Responsive Interface
**Name:** Web-Based Mobile-Responsive Interface  
**Description:** The system should be web-based and mobile-responsive to allow access from various devices and screen sizes.

## 14. Secure Authentication System
**Name:** Secure Authentication System  
**Description:** Must support secure login/logout functionality for all user roles with appropriate access controls and security measures.

## 15. Automated Email Notifications
**Name:** Automated Email Notifications  
**Description:** Notification emails must be automated and configurable to keep users informed about leave request status changes and attendance issues.

## 16. Multi-Language Support
**Name:** Multi-Language Support  
**Description:** The system should be localized and support multiple languages as needed for international operations.

## 17. Multiple Leave Types Support
**Name:** Multiple Leave Types Support  
**Description:** The system must support various leave types including Paid Leave, Unpaid Leave, Maternity Leave, Paternity Leave, Sick Leave (with Hospital Certificate), Bereavement Leave, Study Leave, and Personal Leave.

## 18. Document Upload Requirement
**Name:** Document Upload Requirement  
**Description:** For certain leave types that require documentation (such as Medical Leave with Hospital Certificate), employees must be able to upload required documents as defined in system settings.

## 19. Policy Compliance Validation
**Name:** Policy Compliance Validation  
**Description:** The system should perform rule-based validation against company leave policies while still allowing submission of requests that violate policy for manual review.

## 20. Monthly Deadline Enforcement
**Name:** Monthly Deadline Enforcement  
**Description:** All review and approval actions must be completed within the calendar month of the leave start date to ensure timely processing of requests.