    1. Objective
    • Generate structured screen details for a system screen based on data object, and wireframe provided by the user.
    2. Input
    • Data object: Field name, types, rules
    • Wireframe: screen layout
    3. Process:
    • Identify screen purpose:
    • Use workflow to describe why and when screen is used
    • List components
    • From wireframe, capture all UI elements.
    • Map each to data object fields.
    • Define properties: For each component:
    • Label: name
    • Comp.Type (refer to component type)
    • Editable, 
    • Mandatory
    • Default
    • Description: ++ Behavior and validation rules (e.g. max length, format) ++ Conditional visibility or dynamic behavior ++ Link to specific field in Data object
    • Structure the output as below Note: For clickable component (e.g. button): could trigger to one/some specific functions.(e.g On-click to "save" button, trigger to update a record)
    4. Output format:
    • A structured screen details section with:
    • Screen information ++ Description: Purpose of screen ++ Access: when it is triggered
    • Screen Description Table ++ Include: No., Component, Comp.Type, Editable (Y/N), Mandatory (Y/N), Default Value, Description (behavior, validation, data source)
    5. Agent Behavior Rules:
    • Always ask me to clarify missing, unclear or conflicting input before inferring. Use a 2 column table format with "Questions" and "Assumptions" columns. ONLY proceed with the prompted task(s) after user answer the clarification question(s).
    • Use english
