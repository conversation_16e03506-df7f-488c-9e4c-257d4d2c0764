services:
  mongodb:
    image: mongo:8.0
    restart: unless-stopped
    ports:
      - "27017:27017"
  # volumes:
  #   - mongodb_data:/data/db
  # networks:
  #   - agent-api

  vectordb:
    image: mongodb/mongodb-atlas-local:latest
    container_name: vectordb-knowledge
    restart: unless-stopped
    ports:
      - "27018:27017"
  # volumes:
  #   - vector_data:/data/db
  # networks:
  #   - agent-api

  phoenix:
    image: arizephoenix/phoenix:latest
    ports:
      - "6006:6006" # UI and OTLP HTTP collector
      - "4317:4317" # OTLP gRPC collector
    # networks:
    #   - agent-api

  fastapi-app:
    build:
      context: .
      dockerfile: Dockerfile
    image: ${IMAGE_NAME:-agent-api}:${IMAGE_TAG:-latest}
    command: uv run uvicorn app.main:app --host 0.0.0.0 --port 8080 --reload
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - .:/app # your code (for hot-reload)
      - uv_cache:/app/.cache/uv # <--- mount writable cache here
      - uv_share:/app/.local/share/uv
      - uv_venv:/app/.venv
      - nltk_data:/app/nltk_data # <--- mount NLTK data directory
    environment:
      - NLTK_DATA=/app/nltk_data # <--- set NLTK data path
    # networks:
    #   - agent-api
    depends_on:
      - mongodb
      - vectordb
      - phoenix
    extra_hosts:
      - "host.docker.internal:host-gateway"
    env_file:
      - .env

volumes:
  # mongodb_data:
  # vector_data:
  uv_cache: # <--- define the named volume
  uv_share:
  uv_venv:
  nltk_data: # <--- NLTK data volume

# networks:
#   agent-api:
