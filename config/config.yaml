retrieval_config:
  # qdrant, cosmos_mongo, azure_search
  provider: "qdrant"
  # similarity, mmr
  search_type: "similarity"
  # the parameter that controls the influence of each rank position in rrf.
  rrf_k: 60
  # search_kwargs for Retriever
  kwargs:
    # top-k of restriever for each query that generated from original question
    k: 15
    score_threshold: 0.3
    # fetch_k: 20
    # lambda_mult: 0.5

chat_model_config:
  # actual top-k documents use as context
  top_k: 10
  # kwargs for ChatModel
  kwargs:
    temperature: 0.1
    max_tokens: 3000
    top_p: 0.7
    max_retries: 1

embedding_model_config:
  provider: "azure_openai"
  deployment_name: "text-embedding-3-small"
  # kwargs for EmbeddingModel
  kwargs:
    chunk_size: 2048

# Knowledge base configuration for agents
knowledge_base_config:
  # search type for vector database (vector, keyword, hybrid)
  search_type: "hybrid"
  # number of documents to retrieve from knowledge base
  num_documents: 5
  # maximum chunk size for document processing
  chunk_size: 5000
  # overlap between chunks during document processing
  chunk_overlap: 500

# List of available chat models for use in the application
available_chat_models:
  - gpt-4.1-mini
  - gpt-4.1
  - gpt-4o
common_chat_model: gpt-4o
cost_per_1m_prompt_tokens:
  gpt-4.1-nano: 0.10
  gpt-4.1-mini: 0.40
  gpt-4.1: 2.00
  gpt-4o: 2.50
  gpt-o4-mini: 1.10
cost_per_1m_completion_tokens:
  gpt-4.1-nano: 0.40
  gpt-4.1-mini: 1.60
  gpt-4.1: 8.00
  gpt-4o: 10.00
  gpt-o4-mini: 4.40
cost_per_1m_cached_tokens:
  gpt-4.1-nano: 0.025
  gpt-4.1-mini: 0.10
  gpt-4.1: 0.50
  gpt-4o: 1.25
  gpt-o4-mini: 0.275