#!/usr/bin/env python3
"""
Enhanced Agent Seed <PERSON>t with Versioning Support

This script provides comprehensive agent data management:
1. Seeds MongoDB with sample agent documents
2. Migrates existing agents to versioning system
3. Fixes database indexes for versioning support

Usage:
    # Inside Docker container - run all operations
    docker compose exec fastapi-app uv run python enhanced_seed_agents.py

    # With specific operations
    docker compose exec fastapi-app uv run python enhanced_seed_agents.py --fix-indexes
    docker compose exec fastapi-app uv run python enhanced_seed_agents.py --migrate-versions
    docker compose exec fastapi-app uv run python enhanced_seed_agents.py --seed-sample-data
"""

import asyncio
import argparse
import os
import sys
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import OperationFailure, DuplicateKeyError


class AgentSeeder:
    def __init__(self):
        # MongoDB connection settings
        self.mongodb_url = os.getenv("MONGODB_URL", "mongodb://mongodb:27017")
        self.database_name = os.getenv("DATABASE_NAME", "bavista")
        self.client = None
        self.db = None
        self.agents_collection = None

    async def connect(self):
        """Connect to MongoDB"""
        print(f"🔌 Connecting to MongoDB: {self.mongodb_url}")
        print(f"📊 Database: {self.database_name}")
        
        self.client = AsyncIOMotorClient(self.mongodb_url)
        self.db = self.client[self.database_name]
        self.agents_collection = self.db.agents
        
        try:
            await self.client.admin.command('ping')
            print("✅ MongoDB connection successful")
            return True
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False

    async def close(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            print("🔌 MongoDB connection closed")

    async def fix_indexes(self):
        """Fix MongoDB indexes for versioning support"""
        print("\n🔧 Fixing MongoDB indexes...")
        
        try:
            # Get current indexes
            current_indexes = await self.agents_collection.list_indexes().to_list(length=None)
            index_names = [idx["name"] for idx in current_indexes]
            
            print("Current indexes:")
            for idx in current_indexes:
                index_type = "COMPOUND" if len(idx.get('key', {})) > 1 else "SINGLE"
                unique_str = "UNIQUE" if idx.get('unique', False) else "NON-UNIQUE"
                print(f"  - {idx['name']}: {idx.get('key', {})} ({index_type}, {unique_str})")
            
            # Drop old unique code index if it exists
            if "code_1" in index_names:
                print("\n🗑️  Dropping old unique code index...")
                await self.agents_collection.drop_index("code_1")
                print("✅ Dropped old unique code index (code_1)")
            else:
                print("\nℹ️  Old unique code index (code_1) not found")
            
            # Check if compound index exists
            compound_index_exists = any(
                idx.get("key") == {"code": 1, "version": 1} 
                for idx in current_indexes
            )
            
            if not compound_index_exists:
                print("\n🔧 Creating compound (code, version) unique index...")
                await self.agents_collection.create_index(
                    [("code", 1), ("version", 1)], 
                    unique=True,
                    name="code_version_unique"
                )
                print("✅ Created compound (code, version) unique index")
            else:
                print("\nℹ️  Compound (code, version) index already exists")
                
            return True
            
        except Exception as e:
            print(f"❌ Error fixing indexes: {e}")
            return False

    async def migrate_versions(self):
        """Migrate existing agents to versioning system"""
        print("\n📦 Migrating existing agents to versioning system...")
        
        try:
            # Count agents needing migration
            agents_without_version = await self.agents_collection.count_documents({"version": {"$exists": False}})
            agents_with_datetime_version = await self.agents_collection.count_documents({"version": {"$type": "date"}})
            agents_with_zero_version = await self.agents_collection.count_documents({"version": "0.0"})
            
            print(f"📊 Migration summary:")
            print(f"  - Agents without version field: {agents_without_version}")
            print(f"  - Agents with datetime version: {agents_with_datetime_version}")
            print(f"  - Agents with version '0.0': {agents_with_zero_version}")
            
            updated_count = 0
            
            # Update agents without version field
            if agents_without_version > 0:
                result = await self.agents_collection.update_many(
                    {"version": {"$exists": False}},
                    {"$set": {"version": "0.1", "is_active": True}}
                )
                updated_count += result.modified_count
                print(f"✅ Updated {result.modified_count} agents without version field")
            
            # Update agents with datetime version
            if agents_with_datetime_version > 0:
                result = await self.agents_collection.update_many(
                    {"version": {"$type": "date"}},
                    {"$set": {"version": "0.1", "is_active": True}}
                )
                updated_count += result.modified_count
                print(f"✅ Updated {result.modified_count} agents with datetime version")
            
            # Update agents with version "0.0"
            if agents_with_zero_version > 0:
                result = await self.agents_collection.update_many(
                    {"version": "0.0"},
                    {"$set": {"version": "0.1"}}
                )
                updated_count += result.modified_count
                print(f"✅ Updated {result.modified_count} agents with version '0.0'")
            
            # Fix duplicate active versions
            print("\n🔍 Checking for duplicate active versions...")
            pipeline = [
                {"$group": {"_id": "$code", "codes": {"$addToSet": "$code"}}},
                {"$project": {"_id": 0, "code": "$_id"}}
            ]
            
            agent_codes_cursor = self.agents_collection.aggregate(pipeline)
            agent_codes = [doc["code"] async for doc in agent_codes_cursor]
            
            fixed_duplicates = 0
            for agent_code in agent_codes:
                agent_versions = await self.agents_collection.find({"code": agent_code}).to_list(length=None)
                active_versions = [agent for agent in agent_versions if agent.get("is_active", False)]
                
                if len(active_versions) > 1:
                    # Sort by version and keep highest active
                    def version_sort_key(agent):
                        version = agent.get("version", "0.1")
                        if isinstance(version, str):
                            try:
                                major, minor = version.split('.')
                                return (int(major), int(minor))
                            except (ValueError, IndexError):
                                return (0, 1)
                        return (0, 1)
                    
                    sorted_versions = sorted(active_versions, key=version_sort_key, reverse=True)
                    
                    # Deactivate all except the highest version
                    for i, agent in enumerate(sorted_versions):
                        if i > 0:
                            await self.agents_collection.update_one(
                                {"_id": agent["_id"]},
                                {"$set": {"is_active": False}}
                            )
                            fixed_duplicates += 1
                
                elif len(active_versions) == 0:
                    # No active version, activate the latest
                    if agent_versions:
                        sorted_versions = sorted(agent_versions, key=lambda x: x.get("version", "0.1"), reverse=True)
                        await self.agents_collection.update_one(
                            {"_id": sorted_versions[0]["_id"]},
                            {"$set": {"is_active": True}}
                        )
                        fixed_duplicates += 1
            
            print(f"✅ Fixed {fixed_duplicates} duplicate active version issues")
            
            return {
                "agents_updated_with_version": updated_count,
                "duplicate_active_versions_fixed": fixed_duplicates
            }
            
        except Exception as e:
            print(f"❌ Error during migration: {e}")
            return None

    async def seed_sample_data(self):
        """Seed database with sample agent data"""
        print("\n🌱 Seeding sample agent data...")
        
        sample_agents = [
            {
                "code": "hlr-agent",
                "name": "HLR Agent",
                "description": "High-Level Reasoning agent for complex problem-solving tasks",
                "version": "0.1",
                "is_active": True,
                "system_message": "You are a high-level reasoning agent. Analyze complex problems and provide structured solutions.",
                "llm_config": {
                    "model": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            },
            {
                "code": "ur-agent", 
                "name": "UR Agent",
                "description": "User Request handling agent for processing user interactions",
                "version": "0.1",
                "is_active": True,
                "system_message": "You are a user request agent. Process user requests and provide helpful responses.",
                "llm_config": {
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.5,
                    "max_tokens": 1500
                }
            },
            {
                "code": "master-agent",
                "name": "Master Agent", 
                "description": "Master orchestration agent for coordinating other agents",
                "version": "0.1",
                "is_active": True,
                "system_message": "You are a master agent. Coordinate and orchestrate other agents to complete complex tasks.",
                "llm_config": {
                    "model": "gpt-4",
                    "temperature": 0.3,
                    "max_tokens": 2500
                }
            }
        ]
        
        inserted_count = 0
        updated_count = 0
        
        for agent_data in sample_agents:
            try:
                # Check if agent already exists
                existing_agent = await self.agents_collection.find_one({
                    "code": agent_data["code"],
                    "version": agent_data["version"]
                })
                
                if existing_agent:
                    # Update existing agent
                    await self.agents_collection.update_one(
                        {"_id": existing_agent["_id"]},
                        {"$set": agent_data}
                    )
                    updated_count += 1
                    print(f"✅ Updated agent: {agent_data['code']} v{agent_data['version']}")
                else:
                    # Insert new agent
                    await self.agents_collection.insert_one(agent_data)
                    inserted_count += 1
                    print(f"✅ Inserted agent: {agent_data['code']} v{agent_data['version']}")
                    
            except DuplicateKeyError:
                print(f"⚠️  Agent {agent_data['code']} v{agent_data['version']} already exists")
            except Exception as e:
                print(f"❌ Error seeding agent {agent_data['code']}: {e}")
        
        print(f"\n📊 Seeding results:")
        print(f"  - Inserted: {inserted_count} agents")
        print(f"  - Updated: {updated_count} agents")
        
        return {"inserted": inserted_count, "updated": updated_count}

    async def verify_setup(self):
        """Verify the setup is working correctly"""
        print("\n🔍 Verifying setup...")
        
        try:
            # Count total agents
            total_agents = await self.agents_collection.count_documents({})
            active_agents = await self.agents_collection.count_documents({"is_active": True})
            
            print(f"📊 Agent statistics:")
            print(f"  - Total agents: {total_agents}")
            print(f"  - Active agents: {active_agents}")
            
            # Show sample agents
            print("\n📋 Sample active agents:")
            active_agents_cursor = self.agents_collection.find({"is_active": True}).limit(5)
            async for agent in active_agents_cursor:
                print(f"  - {agent['code']} v{agent['version']}: {agent['name']}")
            
            # Check indexes
            indexes = await self.agents_collection.list_indexes().to_list(length=None)
            compound_index_exists = any(
                idx.get("key") == {"code": 1, "version": 1} 
                for idx in indexes
            )
            
            if compound_index_exists:
                print("✅ Compound (code, version) index is correctly configured")
            else:
                print("❌ Compound index is missing!")
                
            return True
            
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False


async def main():
    parser = argparse.ArgumentParser(description="Enhanced Agent Seed Script")
    parser.add_argument("--fix-indexes", action="store_true", help="Fix MongoDB indexes")
    parser.add_argument("--migrate-versions", action="store_true", help="Migrate existing agents to versioning")
    parser.add_argument("--seed-sample-data", action="store_true", help="Seed sample agent data")
    parser.add_argument("--verify", action="store_true", help="Verify setup")
    
    args = parser.parse_args()
    
    # If no specific operation is requested, run all
    if not any([args.fix_indexes, args.migrate_versions, args.seed_sample_data, args.verify]):
        print("ℹ️  No specific operation requested, running all operations...")
        run_all = True
    else:
        run_all = False
    
    print("🚀 Starting Enhanced Agent Seed Script")
    print("=" * 50)
    
    seeder = AgentSeeder()
    
    try:
        # Connect to MongoDB
        if not await seeder.connect():
            print("❌ Failed to connect to MongoDB")
            sys.exit(1)
        
        success = True
        
        # Fix indexes
        if run_all or args.fix_indexes:
            if not await seeder.fix_indexes():
                success = False
        
        # Migrate versions
        if run_all or args.migrate_versions:
            result = await seeder.migrate_versions()
            if result is None:
                success = False
        
        # Seed sample data
        if run_all or args.seed_sample_data:
            result = await seeder.seed_sample_data()
            if result is None:
                success = False
        
        # Verify setup
        if run_all or args.verify:
            if not await seeder.verify_setup():
                success = False
        
        if success:
            print("\n🎉 All operations completed successfully!")
            print("\n📝 Next steps:")
            print("   1. Test your API endpoints")
            print("   2. Verify agent versioning works correctly") 
            print("   3. Check that PATCH requests create new versions")
            print("   4. Use GET /v1/agents to see only active versions")
        else:
            print("\n⚠️  Some operations failed. Check the logs above.")
            
    except Exception as e:
        print(f"❌ Script failed with error: {e}")
        sys.exit(1)
        
    finally:
        await seeder.close()


if __name__ == "__main__":
    asyncio.run(main())
