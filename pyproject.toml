[project]
name = "ba-vista-ai"
version = "0.1.0"
description = ""
authors = [
    {name = "DaoDM2", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.115.8",
    "uvicorn[standard]>=0.34.3",
    "pymongo[srv]>=4.12.0",
    "structlog>=25.2.0",
    "python-multipart>=0.0.20",
    "requests>=2.32.3",
    "opentelemetry-api>=1.34.1",
    "opentelemetry-sdk>=1.34.1",
    "openai>=1.90.0",
    "motor>=3.7.1",
    "pyhumps>=3.8.0",
    "cachetools>=6.1.0",
    "jose>=1.0.0",
    "python-jose>=3.5.0",
    "beanie>=1.30.0",
    "openinference-instrumentation-agno>=0.1.8",
    "arize-phoenix-otel>=0.12.1",
    "azure-storage-blob>=12.25.1",
    "unstructured>=0.18.9",
    "markdown>=3.8.2",
    "agno>=1.7.5",
    "markitdown[docx,pdf,xls,xlsx]>=0.1.2",
    "qdrant-client>=1.15.1",
]

[project.optional-dependencies]
dev = [
    "pre-commit>=4.1.0",
    "black>=25.1.0",
    "isort>=6.0.1",
    "autoflake>=2.3.1",
    "flake8>=7.1.2",
    "mypy>=1.15.0",
    "types-PyYAML>=6.0.12",
]



[tool.isort]
profile = "black"
line_length = 120
known_third_party = [
    "fastapi", "uvicorn", "pymongo", "structlog", "python-multipart",
    "requests", "agno", "opentelemetry-api", "opentelemetry-sdk", "openai",
    "pre-commit", "black", "isort", "autoflake", "flake8",
    "mypy", "types-PyYAML", "motor"
]
known_first_party = ["app"]
src_paths = ["app", "pkg", "scripts"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
force_sort_within_sections = true

[tool.mypy]
files = "app"
ignore_missing_imports = true
