from fastapi import status
from starlette.exceptions import HTTPException as StarletteHTTPException

from pkg.message import ErrorMessage


class HttpException(StarletteHTTPException):
    def __init__(self, status_code: int, detail: str, params=None, metadata=None, error_code: str = None):
        if metadata is None:
            metadata = {}
        if params is None:
            params = {}
        self.error_code = error_code
        self.detail = detail
        if isinstance(detail, ErrorMessage):
            self.error_code = detail.name
            self.detail = detail.value.format(**params)
        self.metadata = metadata
        super().__init__(status_code=status_code, detail=self.detail)

    @classmethod
    def not_authenticated(
        cls, message: ErrorMessage | str = ErrorMessage.NOT_AUTHENTICATED, params: dict = {}, **kwargs
    ):
        return cls(status_code=status.HTTP_401_UNAUTHORIZED, detail=message, params=params, **kwargs)

    @classmethod
    def forbidden(cls, message: ErrorMessage | str = ErrorMessage.UNAUTHORIZED, params: dict = {}, **kwargs):
        return cls(status_code=status.HTTP_403_FORBIDDEN, detail=message, params=params, **kwargs)

    @classmethod
    def not_found(cls, message: ErrorMessage | str = ErrorMessage.NOT_FOUND, params: dict = {}, **kwargs):
        return cls(status_code=status.HTTP_404_NOT_FOUND, detail=message, params=params, **kwargs)

    @classmethod
    def bad_request(cls, message: ErrorMessage | str = ErrorMessage.BAD_REQUEST, params: dict = {}, **kwargs):
        return cls(status_code=status.HTTP_400_BAD_REQUEST, detail=message, params=params, **kwargs)

    @classmethod
    def internal_server_error(
        cls, message: ErrorMessage | str = ErrorMessage.INTERNAL_SERVER_ERROR, params: dict = {}, **kwargs
    ):
        return cls(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=message, params=params, **kwargs)

    @classmethod
    def from_starlette_exception(cls, exception: StarletteHTTPException):
        return cls(status_code=exception.status_code, detail=exception.detail)
