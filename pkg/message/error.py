from enum import Enum


class ErrorMessage(str, Enum):
    NOT_AUTHENTICATED = "You are not authenticated"
    UNAUTHORIZED = "You are not authorized to access this resource"
    UNAUTHORIZED_PERMISSION = "You do not have permission `{permission}` to access this resource"
    NOT_FOUND = "The requested resource was not found"
    BAD_REQUEST = "The request was invalid"
    INTERNAL_SERVER_ERROR = "An internal server error occurred"

    AGENT_NOT_FOUND = "Project not found or has no agents"
    CONVERSATION_CREATE_FAILED = "Failed to create conversation"

    AGENT_NOT_EXISTS = "Agent `{code}` does not exists"
    CONVERSATION_NOT_EXISTS = "Conversation does not exists"
    MESSAGE_NOT_EXISTS = "Message does not exists"
    FILE_NOT_EXISTS = "File does not exists"
    FILE_NOT_IN_CREATED_STATUS = "File is not in created status"
    FILE_NOT_UPLOADED = "File is not uploaded"
