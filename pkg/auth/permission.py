from functools import wraps
from typing import Callable

from pkg.exception.http_exception import HttpException
from pkg.message.error import ErrorMessage


# TODO: Write fastapi to check project membership
def has_permission(permission: str):
    def check_permission(user: dict):
        if not user:
            return False
        if user.get("is_admin", False):
            return True
        if not user.get("authorities", []):
            return False
        return permission in user["authorities"]

    def has_permission_decorator(function: Callable):
        @wraps(function)
        def wrapper(*args, **kwargs):
            if not check_permission(kwargs.get("user")):
                raise HttpException.forbidden(
                    message=ErrorMessage.UNAUTHORIZED_PERMISSION, params={"permission": permission}
                )
            return function(*args, **kwargs)

        return wrapper

    return has_permission_decorator
