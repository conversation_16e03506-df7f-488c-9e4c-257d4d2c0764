from cachetools import <PERSON><PERSON><PERSON><PERSON>, cached
from fastapi import Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import httpx
from jose import jwt

from pkg.exception.http_exception import HttpException

from .user import User


def get_token(request: Request):
    if request.headers.get("Authorization"):
        return request.headers.get("Authorization").split(" ")[1]
    return None


class JWTBearer(HTTPBearer):
    def __init__(
        self,
        secret_key: str = None,
        jwks_uri: str = None,
        is_verify: bool = True,
        issuer: str = None,
        audience: str = None,
    ):
        super().__init__()
        self.secret_key = secret_key
        self.jwks_uri = jwks_uri
        self.is_verify = is_verify
        self.issuer = issuer
        self.audience = audience

        if not self.secret_key and not self.jwks_uri:
            raise Exception("JWTBearer requires either a secret key or a JWKS URI")

    async def __call__(self, request: Request):
        try:
            credentials = await super().__call__(request)
            if credentials:
                if not credentials.scheme == "Bearer":
                    raise HttpException.not_authenticated()
                token = credentials.credentials
                claims = await self.decode_jwt(token)
                return User(**claims)
            else:
                raise HttpException.not_authenticated()
        except Exception:
            raise HttpException.not_authenticated()

    async def decode_jwt(self, jwt_token: str):
        key = self.secret_key
        if self.jwks_uri and not key:
            key = self.__get_public_key()
        return jwt.decode(
            token=jwt_token,
            key=key,
            issuer=self.issuer,
            options={
                "verify_signature": self.is_verify,
                "verify_iss": bool(self.issuer),
                "verify_aud": bool(self.audience),
                "verify_at_hash": False,
            },
        )

    @cached(cache=TTLCache(maxsize=1, ttl=3600))
    def __get_public_key(self):
        with httpx.Client() as client:
            response = client.get(self.jwks_uri)
            return response.json()
