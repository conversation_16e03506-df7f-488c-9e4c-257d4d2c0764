from pkg.response.http import HttpResponse

from .model import BaseModel


class Pagination:
    def __init__(self, data: list[BaseModel], total: int = None, limit: int = None, offset: int = None):
        self.data = data
        self.total = total
        self.limit = limit
        self.offset = offset

    @classmethod
    def of(cls, data: list[dict | BaseModel], total: int = None, limit: int = None, offset: int = None):
        return cls(data=data, total=total, offset=offset, limit=limit)

    def to_response(self):
        data = [item.model_dump(by_alias=True) if isinstance(item, BaseModel) else item for item in self.data]
        metadata = {
            "total": self.total if self.total else len(data),
            "offset": self.offset,
            "limit": self.limit if self.limit else None,
        }
        return HttpResponse.of(data=data, metadata=metadata)
