from fastapi import status
from humps import camelize
from pydantic import BaseModel as PydanticBaseModel

from pkg.response.http import HttpResponse


class BaseModel(PydanticBaseModel):
    class Config:
        alias_generator = camelize
        populate_by_name = True

    def to_response(self, status_code: int = status.HTTP_200_OK, metadata: dict = None):
        return HttpResponse.of(data=self.model_dump(by_alias=True), status_code=status_code, metadata=metadata)
