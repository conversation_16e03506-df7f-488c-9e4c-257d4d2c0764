from enum import Enum
from typing import Optional


class SortOrder(Enum):
    asc = "asc"
    desc = "desc"


class Pageable:
    def __init__(
        self,
        limit: Optional[int],
        offset: Optional[int],
        sort_by: Optional[str] = None,
        sort_order: Optional[SortOrder] = SortOrder.desc,
    ):
        self.limit = 20 if limit is None else limit
        self.offset = 0 if offset is None else offset
        self.sort_by = sort_by
        self.sort_order = 1 if sort_order == SortOrder.asc else -1
