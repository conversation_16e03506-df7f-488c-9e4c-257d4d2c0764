from typing import Generic, List, TypeVar

from fastapi import status
from fastapi.encoders import jsonable_encoder
import fastapi.responses as fastapi_responses
from pydantic import BaseModel, Field

from pkg.exception.http_exception import HttpException
from pkg.message import Message

T = TypeVar("T")


class PaginationMetadata(BaseModel):
    total: int = Field(..., description="The total number of results")
    offset: int = Field(0, description="The offset for pagination")
    limit: int = Field(20, description="The limit for pagination, if applicable")


class HttpResponse:
    class Response(BaseModel, Generic[T]):
        data: T = None
        metadata: dict = None
        message: str = Message.OK
        error_code: str = None

    class PaginatedResponse(BaseModel, Generic[T]):
        data: List[T] = []
        metadata: PaginationMetadata = None
        message: str = Message.OK
        error_code: str = None

    def __init__(
        self,
        data: dict,
        status_code: int = status.HTTP_200_OK,
        metadata: dict = None,
        message: str = None,
        error_code: str = None,
    ):
        self.data = data
        self.status_code = status_code
        self.metadata = metadata
        self.message = message
        self.error_code = error_code

    def to_json(self):
        response: dict = {}
        if self.data is not None:
            response["data"] = self.data
        if self.metadata is not None:
            response["metadata"] = self.metadata
        if self.message is not None:
            response["message"] = self.message
        if self.error_code is not None:
            response["error"] = self.error_code
        return response

    def to_response(self):
        return fastapi_responses.JSONResponse(content=jsonable_encoder(self.to_json()), status_code=self.status_code)

    @classmethod
    def of(
        cls,
        data=None,
        status_code: int = status.HTTP_200_OK,
        metadata: dict = None,
        message: str = Message.OK,
        error_code: str = None,
    ):
        return cls(
            data=data, status_code=status_code, metadata=metadata, message=message, error_code=error_code
        ).to_response()

    @classmethod
    def ok(cls, message: str = Message.OK):
        return cls.of(status_code=status.HTTP_200_OK, message=message)

    @classmethod
    def from_exception(cls, exception: HttpException):
        return cls.of(
            data=None,
            status_code=exception.status_code,
            metadata=exception.metadata,
            message=exception.detail,
            error_code=exception.error_code,
        )
