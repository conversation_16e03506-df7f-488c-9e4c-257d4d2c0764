from datetime import datetime, timedelta
import logging
from typing import Optional

from azure.storage.blob import BlobSasPermissions, BlobServiceClient, generate_blob_sas

from app.config import APP_CONFIG

logger = logging.getLogger(__name__)


class AzureBlobService:
    def __init__(
        self,
        account_name: str = APP_CONFIG.file_config.blob_account_name,
        account_key: str = APP_CONFIG.file_config.blob_account_key,
        container_name: str = APP_CONFIG.file_config.blob_container_name,
    ):
        self.account_name = account_name
        self.account_key = account_key
        self.container_name = container_name
        blob_service_client = BlobServiceClient.from_connection_string(
            conn_str=f"DefaultEndpointsProtocol=https;AccountName={account_name};AccountKey={account_key};EndpointSuffix=core.windows.net"
        )
        self.client = blob_service_client.get_container_client(container_name)

    async def generate_presigned_url(
        self,
        blob_name: str,
        token_expiry=APP_CONFIG.file_config.pre_signed_url_expiry,
        content_type: Optional[str] = None,
        size: Optional[int] = None,
        permission: BlobSasPermissions = BlobSasPermissions(read=True),
        file_name: Optional[str] = None,
        download: bool = True,
    ) -> str:
        sas_token = generate_blob_sas(
            account_name=self.account_name,
            account_key=self.account_key,
            container_name=self.container_name,
            blob_name=blob_name,
            permission=permission,
            expiry=datetime.now() + timedelta(seconds=token_expiry),
            content_type=content_type,
            size=size,
        )
        return f"https://{self.account_name}.blob.core.windows.net/{self.container_name}/{blob_name}?{sas_token}"

    async def get_blob(self, blob_name: str):
        try:
            blob_client = self.client.get_blob_client(blob_name)
            blob_data = blob_client.download_blob().readall()
            return blob_data
        except Exception as e:
            logger.error(f"Error retrieving blob {blob_name}: {e}")
            return None

    async def upload_blob(self, blob_name: str, file_path: str):
        try:
            with open(file_path, "rb") as data:
                self.client.upload_blob(blob_name, data, overwrite=True)
        except Exception as e:
            logger.error(f"Error uploading blob {blob_name}: {e}")

    async def delete_blob(self, blob_name: str):
        try:
            self.client.delete_blob(blob_name)
        except Exception as e:
            logger.error(f"Error deleting blob {blob_name}: {e}")

    async def check_blob_exists(self, blob_name: str) -> bool:
        try:
            blob_client = self.client.get_blob_client(blob_name)
            return blob_client.exists()
        except Exception as e:
            logger.error(f"Error checking existence of blob {blob_name}: {e}")
            return False

    @staticmethod
    def get_uploadable_permission():
        return BlobSasPermissions(write=True, create=True)
