from fastapi import Depends

from app.config import APP_CONFIG
from pkg.auth.jwt import get_token

from .base import Base


class Sample(Base):
    def __init__(self, service_uri: str = APP_CONFIG.requirement_tool_uri, token: str = Depends(get_token), **kwargs):
        super().__init__(service_uri=service_uri, token=token, **kwargs)

    async def get_sample(self):
        response = await self.client.get("/")
        response.raise_for_status()

        return response.json()
