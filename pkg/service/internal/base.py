from httpx import AsyncClient


class Base:
    def __init__(self, service_uri: str, **kwargs):
        self.service_uri = service_uri
        self.client = AsyncClient(base_url=service_uri, headers=self.headers(**kwargs))

    @staticmethod
    def headers(**kwargs):
        _headers = {}
        if kwargs.get("token"):
            _headers["Authorization"] = f"Bearer {kwargs.get('token')}"
        return _headers
