trigger:
  branches:
    include:
      - dev
  paths:
    exclude:
      - README.md
      - docs/*

pr:
  branches:
    include:
      - master
      - dev
      - uat

pool:
  name: vm

variables:
  # Container registry service connection established during pipeline creation
  dockerRegistryServiceConnection: 'orca-connection'
  imageRepository: 'orca-ai'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.SourceVersion)'

  # ArgoCD configuration
  argoCDRepo: 'BA-VISTA/infrastructure' # URL of the ArgoCD repository
  argoCDRepoBranch: 'master'
  ${{ if eq(variables['Build.SourceBranchName'], 'uat') }}:
    targetYamlFilePath: 'values/orca-uat-ai.yaml'
  ${{ else }}:
    targetYamlFilePath: 'values/orca-dev-ai.yaml'
  targetYamlKey: '.image.tag'

  # Agent VM image name
  vmImageName: 'ubuntu-latest'

stages:
  - stage: Build
    displayName: Build and push stage
    jobs:
      - job: Build
        displayName: Build
        steps:
          - task: Docker@2
            displayName: Build and push an image to container registry
            inputs:
              command: buildAndPush
              repository: $(imageRepository)
              dockerfile: $(dockerfilePath)
              containerRegistry: $(dockerRegistryServiceConnection)
              tags: |
                $(tag)
                latest


  - stage: UpdateArgoCDImageTag
    displayName: 'Update ArgoCD Image Tag'
    jobs:
      - job: UpdateManifest
        displayName: 'Update Application Manifest'
        workspace:
          clean: "all"
        steps:
          - checkout: git://$(argoCDRepo)
            displayName: 'Checkout ArgoCD Repository'
            persistCredentials: true
            path: s/argocd
          - task: Bash@3
            displayName: 'Clone and Update ArgoCD Repository'
            inputs:
              targetType: 'inline'
              script: |
                echo "##[section]Configuring Git for ArgoCD repository..."
                git config --global user.email "<EMAIL>"
                git config --global user.name "Azure DevOps Pipeline"
                
                cd "$(Pipeline.Workspace)/s/argocd"
                git checkout -b $(argoCDRepoBranch) 2> /dev/null
                
                echo "Current directory: $(pwd)"
                echo "Listing files in current directory:"
                ls -la
                
                if [ ! -f "$(targetYamlFilePath)" ]; then
                  echo "##[error]Target YAML file not found: $(targetYamlFilePath)"
                  exit 1
                fi
                
                echo "##[section]Updating image tag in $(targetYamlFilePath) at path $(targetYamlKey) to $(tag) using yq..."
                yq e -i '$(targetYamlKey) = "$(tag)"' $(targetYamlFilePath)
                if [ $? -ne 0 ]; then
                  echo "##[error]Failed to update image tag using yq."
                  exit 1
                fi
                
                echo "##[section]Displaying diff to confirm changes..."
                git diff
                
                echo "##[section]Committing changes..."
                git add "$(targetYamlFilePath)"
                git commit -m "Azure DevOps Pipeline: Update image tag of ai-service to $(tag)"
                if [ $? -ne 0 ]; then
                  echo "##[warning]No changes to commit or commit failed. Continuing to push in case of prior changes."
                fi
                
                echo "##[section]Pushing changes to $(argoCDRepoBranch) branch..."
                git push origin $(argoCDRepoBranch)
                if [ $? -ne 0 ]; then
                  echo "##[error]Failed to push changes to ArgoCD repository."
                  exit 1
                fi
                
                echo "##[section]Image tag updated successfully in ArgoCD repository!"
              failOnStderr: false
