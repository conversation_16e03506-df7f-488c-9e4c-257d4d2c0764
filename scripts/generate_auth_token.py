from datetime import datetime, timed<PERSON>ta
import os

from dotenv import load_dotenv
from jose import jwt

load_dotenv()


def generate_auth_token(user_id: str = "test-user-123"):
    """Generate authentication token for API testing"""

    # Get config values from your environment
    issuer = os.getenv("JWT_ISSUER", "https://dev-auth.example.com/")
    audience = os.getenv("AUDIENCE", "ba-vista-ai-local")

    payload = {
        "sub": user_id,
        "email": f"{user_id}@example.com",
        "name": "Test User",
        "iss": issuer,
        "aud": audience,
        "iat": datetime.now(),
        "exp": datetime.now() + timedelta(hours=24),
        "user_id": user_id,  # Your app uses this
    }

    # Simple secret for testing (since is_verify=False)
    secret = "test-secret-key-123"
    token = jwt.encode(payload, secret, algorithm="HS256")

    print("=== Generated Auth Token ===")
    print(f"User ID: {user_id}")
    print(f"Token: {token}")
    print(f"Expires: {payload['exp']}")
    print("\n=== Usage ===")
    print(f'curl -H "Authorization: Bearer {token}" http://localhost:8000/conversations')
    print("\n=== For Postman/Insomnia ===")
    print(f"Authorization: Bearer {token}")

    return token


if __name__ == "__main__":
    import sys

    user_id = sys.argv[1] if len(sys.argv) > 1 else "test-user-123"
    generate_auth_token(user_id)
