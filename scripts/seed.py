#!/usr/bin/env python3
"""
Script to seed MongoDB with sample agent documents
Each agent is a standalone document with project_id
Collection: project_agents
"""

import asyncio
import os

from beanie import init_beanie
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

from app.constant import AgentCode
from app.model import Agent, app_models
from app.orchestrator.agents.hlr_agent.prompts import (
    HLR_AGENT_DESCRIPTION,
    HLR_AGENT_ROLE,
    HLR_AGENT_SYSTEM_INSTRUCTION,
)
from app.orchestrator.agents.master_agent.prompts import (
    MASTER_AGENT_DESCRIPTION,
    MASTER_AGENT_ROLE,
    MASTER_AGENT_SYSTEM_INSTRUCTION,
)
from app.orchestrator.agents.ur_agent.prompts import (
    UR_AGENT_DESCRIPTION, 
    UR_AGENT_ROLE, 
    UR_AGENT_SYSTEM_INSTRUCTION
)
from app.orchestrator.agents.usecase_agent.prompts import (
    USECASE_AGENT_DESCRIPTION,
    USECASE_AGENT_ROLE,
    USECASE_AGENT_SYSTEM_INSTRUCTION,
)
from app.orchestrator.agents.screen_agent.prompts import (
    SCREEN_AGENT_DESCRIPTION,
    SCREEN_AGENT_ROLE,
    SCREEN_AGENT_SYSTEM_INSTRUCTION,
)

load_dotenv(override=True)


MONGO_URL = os.getenv("MONGO_URL", "mongodb://localhost:27017")
# MONGO_URL = "mongodb://localhost:27017"
DATABASE_NAME = os.getenv("MONGODB_DATABASE_NAME")

async def seed_project_agents():
    client = AsyncIOMotorClient(MONGO_URL)
    await init_beanie(database=client[DATABASE_NAME], document_models=app_models)
    agents = [
        Agent(
            name="Orca AI",
            code=AgentCode.Master,
            description=MASTER_AGENT_DESCRIPTION,
            role=MASTER_AGENT_ROLE,
            system_prompt=MASTER_AGENT_SYSTEM_INSTRUCTION,
            project="RTMVP",
            additional_prompt="",
            model_id="gpt-4o",
            tools=["router", "logger"],
        ),
        Agent(
            name="User Requirement Agent",
            code=AgentCode.UserRequirement,
            description=UR_AGENT_DESCRIPTION,
            role=UR_AGENT_ROLE,
            system_prompt=UR_AGENT_SYSTEM_INSTRUCTION,
            project="RTMVP",
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
        Agent(
            name="High-Level Requirement Agent",
            code=AgentCode.HighLevelRequirement,
            description=HLR_AGENT_DESCRIPTION,
            role=HLR_AGENT_ROLE,
            system_prompt=HLR_AGENT_SYSTEM_INSTRUCTION,
            project="RTMVP",
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
        Agent(
            name="Use Case Specification Agent",
            code=AgentCode.UseCase,
            description=USECASE_AGENT_DESCRIPTION,
            role=USECASE_AGENT_ROLE,
            system_prompt=USECASE_AGENT_SYSTEM_INSTRUCTION,
            project="RTMVP",
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
        Agent(
            name="Screen Description Agent",
            code=AgentCode.Screen,
            description=SCREEN_AGENT_DESCRIPTION,
            role=SCREEN_AGENT_ROLE,
            system_prompt=SCREEN_AGENT_SYSTEM_INSTRUCTION,
            project="RTMVP",
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
        Agent(
            name="Orca AI",
            code=AgentCode.Master,
            description=MASTER_AGENT_DESCRIPTION,
            role=MASTER_AGENT_ROLE,
            system_prompt=MASTER_AGENT_SYSTEM_INSTRUCTION,
            additional_prompt="",
            model_id="gpt-4o",
            tools=["router", "logger"],
        ),
        Agent(
            name="User Requirement Agent",
            code=AgentCode.UserRequirement,
            description=UR_AGENT_DESCRIPTION,
            role=UR_AGENT_ROLE,
            system_prompt=UR_AGENT_SYSTEM_INSTRUCTION,
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
        Agent(
            name="High-Level Requirement Agent",
            code=AgentCode.HighLevelRequirement,
            description=HLR_AGENT_DESCRIPTION,
            role=HLR_AGENT_ROLE,
            system_prompt=HLR_AGENT_SYSTEM_INSTRUCTION,
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
        Agent(
            name="Use Case Specification Agent",
            code=AgentCode.UseCase,
            description=USECASE_AGENT_DESCRIPTION,
            role=USECASE_AGENT_ROLE,
            system_prompt=USECASE_AGENT_SYSTEM_INSTRUCTION,
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
        Agent(
            name="Screen Description Agent",
            code=AgentCode.Screen,
            description=SCREEN_AGENT_DESCRIPTION,
            role=SCREEN_AGENT_ROLE,
            system_prompt=SCREEN_AGENT_SYSTEM_INSTRUCTION,
            additional_prompt="",
            model_id="gpt-4o",
            tools=["nlp", "parser"],
        ),
    ]

    try:
        print("Clearing existing Agent data...")
        await Agent.delete_all()

        print(f"Inserting {len(agents)} agents...")
        result = await Agent.insert_many(agents)

        print(f"✅ Inserted {len(result.inserted_ids)} agent documents")
        for i, agent in enumerate(agents, 1):
            print(f"{i}. {agent.name} ({agent.code})")

        count = await Agent.count()
        print(f"🔍 Total documents in collection: {count}")

    except Exception as e:
        print(f"❌ Error inserting data: {e}")
    finally:
        client.close()


if __name__ == "__main__":
    print("🚀 Starting MongoDB agent data seeding...")
    asyncio.run(seed_project_agents())
    print("✨ Data seeding completed!")
