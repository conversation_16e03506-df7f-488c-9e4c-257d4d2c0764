# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=api_key_goes_here
AZURE_OPENAI_ENDPOINT=https://<endpoint>.openai.azure.com
AZURE_OPENAI_API_VERSION=2023-12-01-preview
AZURE_EMBEDER_DEPLOYMENT=text-embedding-3-small

# MongoDB Configuration
MONGO_URL=mongodb://localhost:27017
MONGODB_DATABASE_NAME=bavista

# Qdrant VectorDB Configuration
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=qdrant-api-key
QDRANT_COLLECTION=kb_user_docs

# JWT Config
JWT_JWKS_URI=https://login.windows.net/common/discovery/keys
JWT_ISSUER=https://login.windows.net/common/discovery/keys
JWT_AUDIENCE=https://login.windows.net/common/discovery/keys

PHOENIX_ENABLED=true
PHOENIX_API_KEY=your_phoenix_api_key_here # local will don't need this
PHOENIX_ENDPOINT=http://localhost:4317
PHOENIX_PROJECT_ID=default

BLOB_ACCOUNT_NAME=
BLOB_ACCOUNT_KEY=
BLOB_CONTAINER_NAME=files
PRESIGNED_URL_EXPIRY=3600