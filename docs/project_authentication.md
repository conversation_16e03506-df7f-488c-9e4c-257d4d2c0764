# Project-Based Authentication System

This document describes the new project-based authentication system that integrates with the ORCA backend.

## Overview

The system now supports project-based agent access control by:

1. Validating JWT tokens with an external ORCA backend
2. Checking user project access permissions  
3. Filtering agents based on project membership
4. **Handling URL-encoded project codes** for projects with spaces or special characters

## Environment Variables

Add the following environment variable to your `.env` file:

```env
# External API Configuration
ORCA_BACKEND_URL=https://orca-backend.ops-ai.dev
```

## Project Code URL Encoding

Since project codes can contain spaces and special characters, they must be **URL-encoded** when used in API endpoints.

### Examples:
- `"RTMVP"` → `"RTMVP"` (no encoding needed)
- `"My Project"` → `"My%20Project"` (space encoded as %20)
- `"Test & Development"` → `"Test%20%26%20Development"` (space and & encoded)

### Encoding/Decoding Utilities

Use the utility functions in `app/utils/url_encoding.py`:

```python
from app.utils.url_encoding import encode_project_code, decode_project_code

# Encode for URL use
encoded = encode_project_code("My Project")  # Returns "My%20Project"

# Decode for database queries
decoded = decode_project_code("My%20Project")  # Returns "My Project"
```

## New API Endpoints

### Project-Based Agent Routes

All these routes require JWT authentication and validate that the user has access to the specified project:

#### Get Agents by Project
```
GET /{project_code}/agents
```
Returns all active agents for the specified project.

#### Get Agent Details by Project  
```
GET /{project_code}/agents/{agent_code}?version={version}
```
Returns agent details from the specified project. Version parameter is optional.

#### Get Agent Versions by Project
```
GET /{project_code}/agents/{agent_code}/versions  
```
Returns all versions of an agent from the specified project.

### Default Project Routes (Existing)

These routes continue to work as before, filtering only for "Default" project:

- `GET /agents` - Get all agents from Default project
- `GET /agents/{agent_code}` - Get agent from Default project
- `PATCH /agents/{agent_code}` - Update agent in Default project
- `GET /agents/{agent_code}/versions` - Get agent versions from Default project

## Authentication Flow

1. Client sends JWT token in Authorization header: `Bearer <token>`
2. System extracts token and calls ORCA backend: `GET /api/Auth/Me`
3. ORCA backend returns user info including project access
4. System validates user has access to requested project
5. If valid, returns agents filtered by project

## External API Response Format

The ORCA backend `/api/Auth/Me` endpoint returns:

```json
{
    "userName": "truongph2",
    "fullName": "Truong Pham Huu", 
    "email": "<EMAIL>",
    "projects": [
        {
            "projectId": 2,
            "projectName": "RTMVP",
            "projectCode": "RTMVP", 
            "defaultPaging": 20,
            "methodology": "Agile",
            "roles": ["PM", "BA", "TEST", "DEV", "QA", "Customer", "BA Lead"],
            "lastAccess": "2025-08-05T07:38:05.8244994+00:00"
        }
    ],
    "role": 3
}
```

## Implementation Details

### New Components

1. **ExternalApiClient** (`app/service/external_api.py`)
   - HTTP client for ORCA backend communication
   - Handles JWT token validation and user info retrieval

2. **ProjectAuth** (`app/auth/project_auth.py`) 
   - Authentication dependencies for project-based routes
   - Validates project access and extracts user roles

3. **Agent Model Updates** (`app/model/agent.py`)
   - Added `project` field with default value "Default"
   - Updated compound index to include project

4. **Service Updates** (`app/service/agent.py`)
   - New methods for project-specific agent queries
   - Maintained backward compatibility with Default project

### Database Migration

Run the migration script to add project field to existing agents:

```bash
python app/migrations/add_project_field_migration.py
```

This will set `project="Default"` for all existing agents.

## Usage Examples

### Get agents for RTMVP project

```bash
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:8000/api/v1/RTMVP/agents
```

### Get agents for project with spaces ("My Project")

```bash
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:8000/api/v1/My%20Project/agents
```

### Get specific agent from encoded project

```bash
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:8000/api/v1/Test%20%26%20Development/agents/my-agent-code
```

### JavaScript/Frontend encoding example

```javascript
// Encode project code for URL
const projectCode = "My Project & Development";
const encodedProjectCode = encodeURIComponent(projectCode);
// Result: "My%20Project%20%26%20Development"

// Use in API call
const url = `http://localhost:8000/api/v1/${encodedProjectCode}/agents`;
```

### Error Responses

- `401 Unauthorized` - Invalid or missing JWT token
- `403 Forbidden` - User doesn't have access to requested project  
- `404 Not Found` - Agent not found in specified project
- `502/503/504` - External API errors (ORCA backend issues)

## Security Notes

- All project-based routes require valid JWT authentication
- User access is validated against ORCA backend for each request
- Tokens are passed through to external API for validation
- No local user/project data is cached (always validates with external system)
