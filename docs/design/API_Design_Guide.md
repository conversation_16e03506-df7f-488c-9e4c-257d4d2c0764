# Standardized Process for Designing a RESTful Web API

This document provides a step-by-step guide for designing a RESTful Web API, optimized for AI agents to interpret and implement. It adheres to REST principles, incorporates best practices from the OpenAPI Initiative, and aligns with the API checklist by <PERSON><PERSON>. The process is structured to ensure clarity, machine-readability, and automation compatibility.

## Step 1: Define the API's Purpose and Scope
- **Objective**: Clearly articulate the API's functionality and target audience (e.g., internal systems, external developers, AI agents).
- **Tasks**:
  1. Identify the core resources (e.g., `users`, `orders`, `products`) the API will manage.
  2. Define the business capabilities (e.g., create a user, retrieve order details).
  3. Specify constraints (e.g., performance, scalability, security requirements).
- **Output**: A requirements document with a list of resources and operations.
  - **Example**:
    ```markdown
    Resources:
    - Users: Manage user accounts.
    - Orders: Handle customer orders.
    Operations:
    - Create, read, update, delete (CRUD) users.
    - Create, read, list orders.
    Constraints:
    - Response time < 200ms for 95% of requests.
    - Support 10,000 concurrent users.
    ```

## Step 2: Design Resource-Based URIs
- **Objective**: Create intuitive, resource-oriented URIs following REST principles (uniform interface, statelessness).
- **Tasks**:
  1. Use nouns for resources (e.g., `/users`, `/orders`) and avoid verbs in URIs.
  2. Structure URIs hierarchically for relationships (e.g., `/users/{userId}/orders`).
  3. Use plural nouns for collections (e.g., `/users` for all users, `/users/{id}` for a specific user).
  4. Ensure URIs are consistent and predictable.
- **Constraints**:
  - Avoid query parameters for resource identification (e.g., use `/users/123` instead of `/users?id=123`).
  - Keep URIs simple and under 200 characters.
- **Example**:
  ```markdown
  - Collection: GET /api/v1/users
  - Specific resource: GET /api/v1/users/123
  - Sub-resource: GET /api/v1/users/123/orders
  ```

## Step 3: Map HTTP Methods to CRUD Operations
- **Objective**: Assign appropriate HTTP methods to resource operations, adhering to REST’s uniform interface.
- **Tasks**:
  1. Use standard HTTP methods:
     - `GET`: Retrieve resources (safe, idempotent).
     - `POST`: Create resources or trigger actions.
     - `PUT`: Update resources (idempotent).
     - `DELETE`: Remove resources (idempotent).
     - `PATCH`: Partial updates to resources.
  2. Avoid non-standard methods (e.g., no custom `CREATE` or `UPDATE` methods).
- **Constraints**:
  - Ensure idempotency for `GET`, `PUT`, and `DELETE`.
  - Use `POST` for non-idempotent actions or complex operations.
- **Example**:
  ```markdown
  - GET /api/v1/users: List all users.
  - GET /api/v1/users/123: Retrieve user with ID 123.
  - POST /api/v1/users: Create a new user.
  - PUT /api/v1/users/123: Update user with ID 123.
  - DELETE /api/v1/users/123: Delete user with ID 123.
  ```

## Step 4: Implement HATEOAS (Hypermedia as the Engine of Application State)
- **Objective**: Enable clients to navigate the API dynamically using hypermedia links.
- **Tasks**:
  1. Include a `links` object in responses with relevant URIs and relationships.
  2. Use standard relation types (e.g., `self`, `next`, `related`).
  3. Ensure responses provide enough context for navigation.
- **Constraints**:
  - Every response must include a `self` link.
  - Include navigation links for collections (e.g., `next`, `prev` for pagination).
- **Example**:
  ```json
  {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>",
    "links": [
      { "rel": "self", "href": "/api/v1/users/123" },
      { "rel": "orders", "href": "/api/v1/users/123/orders" }
    ]
  }
  ```

## Step 5: Define Request and Response Formats
- **Objective**: Standardize data formats for consistency and machine-readability.
- **Tasks**:
  1. Use JSON as the primary content type (`application/json`).
  2. Define request/response schemas using OpenAPI (Swagger).
  3. Ensure consistent field naming (e.g., camelCase or snake_case).
  4. Include metadata in responses (e.g., `total`, `count` for collections).
- **Constraints**:
  - Avoid nested objects deeper than three levels.
  - Use ISO 8601 for dates (e.g., `2025-06-26T09:50:00Z`).
- **Example**:
  ```json
  // Request (POST /api/v1/users)
  {
    "name": "Jane Doe",
    "email": "<EMAIL>"
  }
  // Response (GET /api/v1/users/123)
  {
    "id": 123,
    "name": "Jane Doe",
    "email": "<EMAIL>",
    "createdAt": "2025-06-26T09:50:00Z",
    "links": [
      { "rel": "self", "href": "/api/v1/users/123" }
    ]
  }
  ```

## Step 6: Use Appropriate HTTP Status Codes
- **Objective**: Communicate operation outcomes clearly using standard HTTP status codes.
- **Tasks**:
  1. Use standard status codes:
     - `200 OK`: Successful GET, PUT, or PATCH.
     - `201 Created`: Successful POST creating a resource.
     - `204 No Content`: Successful DELETE or PATCH with no response body.
     - `400 Bad Request`: Invalid request payload.
     - `401 Unauthorized`: Authentication required.
     - `403 Forbidden`: Access denied.
     - `404 Not Found`: Resource not found.
     - `429 Too Many Requests`: Rate limit exceeded.
  2. Include error details in the response body for 4xx/5xx codes.
- **Constraints**:
  - Always return a JSON error object with `code`, `message`, and optional `details`.
- **Example**:
  ```json
  // Response (400 Bad Request)
  {
    "code": "INVALID_REQUEST",
    "message": "Email is required",
    "details": { "field": "email", "value": null }
  }
  ```

## Step 7: Implement Versioning
- **Objective**: Ensure backward compatibility and evolution of the API.
- **Tasks**:
  1. Include version in the URI (e.g., `/api/v1/users`).
  2. Maintain at least one previous version for 12 months after a new version release.
  3. Document deprecated endpoints and provide migration guides.
- **Constraints**:
  - Avoid breaking changes within the same major version (e.g., `v1`).
  - Use semantic versioning (e.g., `v1.0`, `v1.1` for minor updates).
- **Example**:
  ```markdown
  Current version: /api/v1/users
  Deprecated: /api/v0/users (support ends 2026-06-26)
  ```

## Step 8: Secure the API
- **Objective**: Protect the API from unauthorized access and attacks.
- **Tasks**:
  1. Require HTTPS for all endpoints.
  2. Implement authentication (e.g., OAuth 2.0, API keys).
  3. Use rate limiting to prevent abuse.
  4. Validate and sanitize all inputs to prevent injection attacks.
  5. Include security headers (e.g., `Content-Security-Policy`, `X-Content-Type-Options`).
- **Constraints**:
  - Enforce strong authentication for all non-public endpoints.
  - Limit payload size to 1MB.
- **Example**:
  ```http
  GET /api/v1/users/123 HTTP/1.1
  Host: api.example.com
  Authorization: Bearer <token>
  Content-Type: application/json
  ```

## Step 9: Document the API with OpenAPI
- **Objective**: Provide comprehensive, machine-readable documentation for AI agents and developers.
- **Tasks**:
  1. Use OpenAPI 3.0 specification to define endpoints, schemas, and examples.
  2. Include descriptions for all endpoints, parameters, and responses.
  3. Host documentation at a predictable URI (e.g., `/api/v1/docs`).
  4. Provide interactive Swagger UI for testing.
- **Constraints**:
  - Ensure all endpoints are documented.
  - Validate OpenAPI schema using tools like Swagger Validator.
- **Example**:
  ```yaml
  openapi: 3.0.3
  info:
    title: Sample API
    version: 1.0.0
  paths:
    /users:
      get:
        summary: List all users
        responses:
          '200':
            description: Successful response
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: '#/components/schemas/User'
  components:
    schemas:
      User:
        type: object
        properties:
          id: { type: integer }
          name: { type: string }
          email: { type: string }
  ```

## Step 10: Test and Validate the API
- **Objective**: Ensure the API meets design specifications and performs reliably.
- **Tasks**:
  1. Write automated tests for all endpoints (unit, integration, end-to-end).
  2. Validate responses against OpenAPI schema.
  3. Test edge cases (e.g., invalid inputs, rate limits, authentication failures).
  4. Perform load testing to verify performance under stress.
- **Constraints**:
  - Achieve 95% test coverage for all endpoints.
  - Ensure no critical security vulnerabilities (e.g., use OWASP ZAP).
- **Example**:
  ```bash
  # Test GET /api/v1/users
  curl -X GET "https://api.example.com/api/v1/users" \
       -H "Authorization: Bearer <token>" \
       -H "Accept: application/json"
  ```

## Step 11: Monitor and Maintain the API
- **Objective**: Ensure ongoing reliability and performance.
- **Tasks**:
  1. Implement logging for all requests and errors.
  2. Monitor key metrics (e.g., latency, error rates, usage).
  3. Provide a status endpoint (e.g., `/api/v1/status`).
  4. Plan for deprecation and communicate changes to users.
- **Constraints**:
  - Log all 4xx/5xx errors with correlation IDs.
  - Maintain uptime of 99.9%.
- **Example**:
  ```json
  // GET /api/v1/status
  {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "99.95%"
  }
  ```

## Checklist for AI Implementation
- **URI Design**: Use nouns, hierarchical structure, and versioning.
- **HTTP Methods**: Map to CRUD operations correctly.
- **HATEOAS**: Include `links` in all responses.
- **Status Codes**: Use standard codes with descriptive error messages.
- **Security**: Enforce HTTPS, authentication, and input validation.
- **Documentation**: Provide OpenAPI 3.0 schema and Swagger UI.
- **Testing**: Automate tests for functionality, performance, and security.
- **Monitoring**: Log requests, monitor metrics, and provide status endpoint.

This process ensures a RESTful API that is consistent, secure, and machine-readable, enabling AI agents to implement and interact with it reliably.