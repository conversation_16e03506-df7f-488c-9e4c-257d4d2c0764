@startuml
title MongoDB Schema - BAVista Agent Service

entity "agent_config" as agent_config {
  + _id : ObjectId /' MongoDB default unique identifier '/
  + name : String /' Human-readable name of the agent '/
  + code : String /' Unique code for referencing the agent internally '/
  + role : String /' Role of the agent in the team: e.g., Leader, Worker '/
  + description : String /' Description of the agent’s responsibility or behavior '/
  + prompt_template : {instructions : String, additional_instruction: String} /' Embedded prompt configuration including instructions and context '/
  + tools : List<String> /' List of supported tools or capabilities (e.g., retriever, summarizer) '/
  + created_at : Date /' Document creation timestamp '/
  + updated_at : Date /' Last updated timestamp '/
}

entity "agent_sessions" as agent_sessions {
  + _id : ObjectId /' Unique session identifier '/
  + _version : Int /' Version control for the session schema or format '/
  + created_at : Date /' When the session started '/
  + updated_at : Date /' Last activity or modification time '/
  + agent_id : String /' ID of the agent running in this session '/
  + user_id : String /' ID of the user interacting with the agent '/
  + session_id : String /' Logical ID to track session continuity '/
  + team_session_id : String /' ID to group multiple agents in the same collaboration session '/
  + memory : JSON /' Runtime memory state of the agent in the session '/
  + session_data : JSON /' General-purpose structured data for the session '/
  + agent_data : JSON /' Specific data relevant to the agent’s execution logic '/
  + extra_data : JSON /' Miscellaneous or future-reserved fields '/
}

entity "user_memories" as user_memories {
  + _id : ObjectId /' Unique memory document ID '/
  + _version : Int /' Versioning for backward compatibility '/
  + created_at : Date /' Timestamp of when this memory was recorded '/
  + updated_at : Date /' Last time this memory was modified '/
  + user_id : String /' User that this memory belongs to '/
  + memory  : JSON /' Serialized memory content — can include tasks, decisions, context '/
  + agent_id : ObjectId /'  Reference to the associated agent '/
  + session_id : ObjectId /' Reference to session that generated the memory '/
}

entity "kb_user_requirement" as kb_user_requirement {
  + _id : ObjectId /' Unique knowledge base entry ID '/
  + created_at : Date /' Entry creation time '/
  + updated_at : Date /' Last update time '/
  + user_id : String /' Owner of this requirement knowledge entry '/
  + project_id : String /' Project the requirement is associated with '/
  + session_id : ObjectId /' Reference to session that submitted this requirement '/
  + type : String /' Classified type (e.g., Feature, Bug, Question) '/
  + chunks : List<String> /' Segmented content for better processing '/
  + embedding_vector : List<Float> /' Vector embedding of content for similarity search '/
}

entity "token_logs" as token_logs {
  + _id : ObjectId /' Unique knowledge base entry ID '/
  + created_at : Date /' Entry creation time '/
  + updated_at : Date /' Last update time '/
  + user_id : String /' ID of the user interacting with the agent '/
  + session_id : String /' Logical ID to track session continuity '/
  + input_tokens : Int /' Token input in the session '/
  + output_tokens : Int /' Token output in the session '/
}

' Corrected and consistent reference links (foreign keys)
user_memories::agent_id --> agent_config::_id : references
user_memories::session_id --> agent_sessions::_id : references
kb_user_requirement::session_id --> agent_sessions::_id : references
agent_sessions::agent_id --> agent_config::_id : references

@enduml