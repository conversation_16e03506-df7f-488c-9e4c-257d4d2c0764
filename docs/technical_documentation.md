# Technical Documentation: BAVista AI

## Table of Contents

1.  [Project Overview](#project-overview)
2.  [System Architecture](#system-architecture)
3.  [Data Processing and Storage](#data-processing-and-storage)
4.  [API or Interface Documentation](#api-or-interface-documentation)
5.  [Core Algorithms or Processes](#core-algorithms-or-processes)
6.  [Integration Components and Extension Services](#integration-components-and-extension-services)
7.  [Error Handling and Edge Cases](#error-handling-and-edge-cases)
8.  [Setup and Configuration](#setup-and-configuration)
9.  [Technology Stack](#technology-stack)
10. [Scalability and Performance](#scalability-and-performance)
11. [Security Considerations](#security-considerations)
12. [Change Log](#change-log)

## Project Overview

The BAVista AI project is an advanced AI-powered system designed to assist businesses in analyzing requirements and generating high-level system specifications. It leverages a multi-agent architecture to provide comprehensive insights into user needs.

### Use Case

A business analyst uses BAVista AI to analyze customer requirements, generating detailed specifications and data models. This reduces manual effort and ensures consistency across projects.

## System Architecture

The system consists of the following components:

* **API Server**: A FastAPI application exposing RESTful APIs.
* **Database**:
  * **PostgreSQL (pgvector)**: Stores vectorized data for similarity searches.
  * **MongoDB**: Stores chat sessions and agent memory.
* **Agents**: Built using the `agno` framework, responsible for core business logic.

### Component Interaction

```
[User] -> [API Server (FastAPI)] -> [Leader Agent]
                                        |
                                        +-> [User Requirement Agent]
                                        |
                                        +-> [High-Level Requirement Agent]
                                              |
                                              +-> [Data Object Tool]
                                              |
                                              +-> [Actor Tool]

[Leader Agent] -> [MongoDB (Session Storage)]
[High-Level Requirement Agent] -> [PostgreSQL (pgvector)]
```

## Data Processing and Storage

### Data Flow

1. User inputs are processed by the Leader Agent.
2. Data is stored in MongoDB for session tracking.
3. Vectorized data is stored in PostgreSQL for similarity searches.

### Sample Schema

```json
{
  "user_id": "12345",
  "session_data": {
    "messages": [
      { "content": "What are the requirements for project X?", "timestamp": "2025-07-15T10:00:00Z" }
    ]
  }
}
```

## API or Interface Documentation

### API Endpoints

| Endpoint               | HTTP Method | Parameters         | Request Body | Response Format | Authentication |
|------------------------|-------------|--------------------|--------------|-----------------|-----------------|
| `/api/v1/users`        | GET         | None               | None         | JSON            | API Key        |
| `/api/v1/users`        | POST        | None               | JSON         | JSON            | API Key        |
| `/api/v1/users/{id}`   | GET         | `id` (Path)        | None         | JSON            | API Key        |
| `/api/v1/users/{id}`   | PATCH       | `id` (Path)        | JSON         | JSON            | API Key        |
| `/api/v1/users/{id}`   | DELETE      | `id` (Path)        | None         | JSON            | API Key        |

### Example Request

```json
POST /api/v1/users
{
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

### Example Response

```json
{
  "id": "12345",
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

## Core Algorithms or Processes

### Artefact Parsing

The `parse_artefact` function processes user-provided content to extract structured data.

#### Pseudocode

```
function parse_artefact(content):
    validate(content)
    parsed_data = extract_entities(content)
    return parsed_data
```

## Integration Components and Extension Services

### Third-Party Libraries

| Library       | Purpose                  | Benefits               | Limitations          |
|---------------|--------------------------|------------------------|----------------------|
| `pgvector`    | Vectorized data storage  | Fast similarity search | Requires PostgreSQL  |
| `FastAPI`     | API framework            | High performance       | Limited ecosystem    |
| `MongoDB`     | NoSQL database           | Flexible schema        | Requires scaling     |

## Error Handling and Edge Cases

### Common Errors

| Error Code | Message                  | Recovery Steps          |
|------------|--------------------------|-------------------------|
| 404        | Resource not found       | Verify resource ID      |
| 500        | Internal server error    | Check server logs       |

### Edge Cases

* **Null Values**: Validate inputs to prevent null pointer exceptions.
* **Unexpected Inputs**: Sanitize user inputs to avoid injection attacks.

## Setup and Configuration

### Prerequisites

* Docker
* Python 3.10+

### Configuration

```yaml
retrieval_config:
  provider: "qdrant"
  search_type: "similarity"
  kwargs:
    k: 15
    score_threshold: 0.3
```

## Technology Stack

| Technology   | Version | Purpose                  |
|--------------|---------|--------------------------|
| Python       | 3.10+   | Backend development      |
| FastAPI      | Latest  | API framework            |
| PostgreSQL   | 14+     | Relational database      |
| MongoDB      | 5.0+    | NoSQL database           |

## Scalability and Performance

### Scalability

* Horizontal scaling using container orchestration.

### Performance

* Average response time: <200ms.

## Security Considerations

### Measures

* Input validation.
* API key authentication.
* Data encryption.

## Change Log

- **Last Updated**: 2025-07-15 11:06 (UTC+7)
- **Added**: Comprehensive API documentation.
- **Updated**: System architecture diagram.
- **Removed**: Outdated references to legacy components.
