import logging

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from app.config.base_config import APP_CONFIG

mongo_config = APP_CONFIG.mongo_config
logger = logging.getLogger(__name__)

# Global state to hold the MongoDB client
_client: AsyncIOMotorClient | None = None


async def connect_to_mongo():
    """
    Initialize MongoDB connection.
    Should be called during app startup.
    """
    global _client
    try:
        _client = AsyncIOMotorClient(
            mongo_config.url,
            serverSelectionTimeoutMS=5000,  # 5 seconds timeout
            connectTimeoutMS=5000,
            socketTimeoutMS=5000,
            maxPoolSize=50,  # Maximum number of connections in the pool
            minPoolSize=3,  # Minimum number of connections in the pool
            maxIdleTimeMS=30000,  # Close connections after 30 seconds of inactivity
        )

        logger.info(f"Successfully connected to MongoDB at {mongo_config.url}")

    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise


async def close_mongo_connection():
    """
    Close MongoDB connection.
    Should be called during app shutdown.
    """
    global _client
    if _client is not None:
        try:
            _client.close()
            logger.info("MongoDB connection closed")
        except Exception as e:
            logger.error(f"Error closing MongoDB connection: {e}")
        finally:
            _client = None


def get_mongo_client() -> AsyncIOMotorClient:
    """
    Get the MongoDB client instance.
    Raises RuntimeError if client is not initialized.
    """
    if _client is None:
        raise RuntimeError(
            "MongoDB client is not initialized. " "Make sure to call connect_to_mongo() during app startup."
        )
    return _client


def get_mongo_database() -> AsyncIOMotorDatabase:
    """
    Get the MongoDB database instance.
    """
    client = get_mongo_client()
    return client[mongo_config.database_name or "default_database"]
