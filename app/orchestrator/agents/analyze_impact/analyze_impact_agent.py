from textwrap import dedent

from agno.team import Team

from app.factories.chat_factory import create_chat_model
from app.orchestrator.agents.analyze_impact.prompts import ANALYZE_IMPACT_LEADER_DESCRIPTION, \
    ANALYZE_IMPACT_LEADER_INSTRUCTION
from app.orchestrator.agents.hlr_agent.agent import HLRAgent
from app.orchestrator.agents.ur_agent.agent import URAgent
from app.orchestrator.tools.req_artefacts import ArtefactsFindingTool

hlr_agent = HLRAgent().agent
ur_agent = URAgent().agent


def init_agent():
    return Team(
        name="Analyze Impact Agent",
        model=create_chat_model(model_id="gpt-o4-mini"),
        tools=[ArtefactsFindingTool()],
        description=dedent(ANALYZE_IMPACT_LEADER_DESCRIPTION),
        instructions=dedent(ANALYZE_IMPACT_LEADER_INSTRUCTION),
        show_tool_calls=True,
        add_history_to_messages=True,
        mode="coordinate",
        debug_mode=True,
        show_members_responses=True,
        share_member_interactions=True,
        enable_agentic_context=True,
        enable_team_history=True,
        members=[hlr_agent, ur_agent]
    )
