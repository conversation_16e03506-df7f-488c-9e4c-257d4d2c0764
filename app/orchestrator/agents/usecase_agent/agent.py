from textwrap import dedent
from typing import (
    List,
    Optional,
)

from app.config.get_config import get_config
from app.factories.chat_factory import create_chat_model
from app.factories.knowledge_base_factory import create_knowledge_base
from app.orchestrator.tools import create_summarizer, CustomAgent
from app.schema import AgentSchema
from app.utils.config_helper import AgentConfigHelper

from .prompts import USECASE_AGENT_DESCRIPTION, USECASE_AGENT_ROLE, USECASE_AGENT_SYSTEM_INSTRUCTION


class UseCaseAgent:
    """Use Case Specification Agent that creates detailed use case documentation with activity flows and business rules"""

    def __init__(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        debug_mode: bool = True,
        agent_config: Optional[AgentSchema] = None,
        file_ids: Optional[List[str]] = None,
    ):
        self.agent_id = agent_id
        self.user_id = user_id
        self.session_id = session_id
        self.debug_mode = debug_mode
        self.file_ids = file_ids

        # Define defaults for this agent
        self.defaults = {
            "model_id": "gpt-4o",
            "name": "Use Case Specification Agent",
            "role": USECASE_AGENT_ROLE,
            "description": USECASE_AGENT_DESCRIPTION,
            "tools": [],
            "system_prompt": USECASE_AGENT_SYSTEM_INSTRUCTION,
            "additional_prompt": "",
        }

        # Extract configuration using helper
        self.config = AgentConfigHelper.extract_config(agent_config, self.defaults)

        # Log initialization
        AgentConfigHelper.log_agent_init("UseCaseAgent", self.agent_id, self.config, bool(agent_config))

        # Agent instance (lazy loaded)
        self._agent_instance = None

    @property
    def agent(self):
        """Get or create the agent instance (lazy loading)"""
        if not self._agent_instance:
            config = get_config().chat_model_config
            knowledge_base = create_knowledge_base()

            # Base agent parameters
            agent_params = {
                "agent_id": self.agent_id,
                "name": self.config["name"],
                "role": self.config["role"],
                "description": self.config["description"],
                "model": create_chat_model(config, model_id=self.config["model_id"]),
                "instructions": dedent(
                    AgentConfigHelper.merge_instructions(self.config["system_prompt"], self.config["additional_prompt"])
                ),
                "add_datetime_to_instructions": True,
                "markdown": True,
                "debug_mode": self.debug_mode,
                "user_id": self.user_id,
                "session_id": self.session_id,
                "add_history_to_messages": True,
                "num_history_runs": 5,
                "read_chat_history": True,
                "knowledge": knowledge_base,
                "search_knowledge": True,
            }

            # Add knowledge parameters if file_ids is provided
            if self.file_ids:
                filters = {"document_id": self.file_ids}
                agent_params.update(
                    {"knowledge_filters": filters, "tools": [create_summarizer(knowledge_base, filters)]}
                )

            self._agent_instance = CustomAgent(**agent_params)
            print("✅ UseCaseAgent instance created successfully")

        return self._agent_instance

    def __repr__(self) -> str:
        return f"UseCaseAgent(id={self.agent_id}, model={self.config['model_id']}, config={'DB' if self.config != self.defaults else 'Default'})"