import asyncio
from typing import (
    Any,
    Dict,
    Iterator,
    Optional,
    Sequence,
    Union,
)
from bson import ObjectId
from agno.agent import Agent
from agno.models.message import Message
from agno.media import Audio, File, Image, Video
from agno.run.response import (
    RunResponse,
    RunResponseEvent,
)

from app.model.file import File
from pkg.service.external import AzureBlobService


class CustomAgent(Agent):
    def run(
        self,
        message: str = None,
        *,
        stream: Optional[bool] = None,
        stream_intermediate_steps: Optional[bool] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        session_state: Optional[Dict[str, Any]] = None,
        audio: Optional[Sequence[Audio]] = None,
        images: Optional[Sequence[Image]] = None,
        videos: Optional[Sequence[Video]] = None,
        files: Optional[Sequence[File]] = None,
        messages: Optional[Sequence[Union[Dict, Message]]] = None,
        retries: Optional[int] = None,
        knowledge_filters: Optional[Dict[str, Any]] = None,
        refresh_session_before_write: Optional[bool] = False,
        **kwargs: Any,
    ) -> Union[RunResponse, Iterator[RunResponseEvent]]:
        # Process knowledge filters to extract images and modify filters
        knowledge_images, updated_filters, search_knowledge = self.process_knowledge_filters(knowledge_filters)
        
        if knowledge_images:
            images = list(images) if images else []
            images.extend(knowledge_images)
        
        # Update kwargs with modified search_knowledge if needed
        if not search_knowledge:
            kwargs['search_knowledge'] = False
            
        return super().run(
            message=message,
            stream=stream,
            stream_intermediate_steps=stream_intermediate_steps,
            user_id=user_id,
            session_id=session_id,
            session_state=session_state,
            audio=audio,
            images=images,
            videos=videos,
            files=files,
            messages=messages,
            retries=retries,
            knowledge_filters=updated_filters,
            refresh_session_before_write=refresh_session_before_write,
            **kwargs
        )
    
    async def arun(
        self,
        message: str = None,
        *,
        stream: Optional[bool] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        session_state: Optional[Dict[str, Any]] = None,
        audio: Optional[Sequence[Audio]] = None,
        images: Optional[Sequence[Image]] = None,
        videos: Optional[Sequence[Video]] = None,
        files: Optional[Sequence[File]] = None,
        messages: Optional[Sequence[Union[Dict, Message]]] = None,
        stream_intermediate_steps: Optional[bool] = None,
        retries: Optional[int] = None,
        knowledge_filters: Optional[Dict[str, Any]] = None,
        refresh_session_before_write: Optional[bool] = False,
        **kwargs: Any,
    ) -> Any:
        # Process knowledge filters to extract images and modify filters
        knowledge_images, updated_filters = await self.async_process_knowledge_filters(knowledge_filters)
        
        if knowledge_images:
            images = list(images) if images else []
            images.extend(knowledge_images)

        return await super().arun(
            message=message,
            stream=stream,
            user_id=user_id,
            session_id=session_id,
            session_state=session_state,
            audio=audio,
            images=images,
            videos=videos,
            files=files,
            messages=messages,
            stream_intermediate_steps=stream_intermediate_steps,
            retries=retries,
            knowledge_filters=updated_filters,
            refresh_session_before_write=refresh_session_before_write,
            **kwargs
        )
    
    def process_knowledge_filters(self, knowledge_filters: Optional[Dict[str, Any]]) -> tuple[list[Image], Optional[Dict[str, Any]], bool]:
        """Process knowledge filters to extract images and modify filters"""
        effective_filters = self._get_effective_filters(knowledge_filters)
        if not effective_filters or "document_id" not in effective_filters:
            return [], effective_filters, True
        
        document_ids = effective_filters["document_id"]
        if isinstance(document_ids, dict) and "$in" in document_ids:
            file_ids = document_ids["$in"]
        elif isinstance(document_ids, list):
            file_ids = document_ids
        else:
            return [], effective_filters, True
        
        images = []
        remaining_file_ids = []
        
        for file_id in file_ids:
            image = asyncio.run(self._get_image_file(file_id))
            if image:
                images.append(image)
            else:
                remaining_file_ids.append(file_id)
        
        # Update filters with remaining file IDs
        updated_filters = None
        search_knowledge = True
        
        if remaining_file_ids:
            updated_filters = effective_filters.copy()
            # Preserve original document_id format
            if isinstance(document_ids, dict):
                updated_filters["document_id"] = {"$in": remaining_file_ids}
            else:  # isinstance(document_ids, list)
                updated_filters["document_id"] = remaining_file_ids
        else:
            # No remaining files, disable knowledge search
            search_knowledge = False
        
        return images, updated_filters, search_knowledge
    
    async def async_process_knowledge_filters(self, knowledge_filters: Optional[Dict[str, Any]]) -> tuple[list[Image], Optional[Dict[str, Any]], bool]:
        """Async process knowledge filters to extract images and modify filters"""
        effective_filters = self._get_effective_filters(knowledge_filters)
        if not effective_filters or "document_id" not in effective_filters:
            return [], effective_filters

        document_ids = effective_filters["document_id"]
        if isinstance(document_ids, dict) and "$in" in document_ids:
            file_ids = document_ids["$in"]
        elif isinstance(document_ids, list):
            file_ids = document_ids
        else:
            return [], effective_filters
        
        images = []
        remaining_file_ids = []

        for file_id in file_ids:
            image = await self._get_image_file(file_id)
            if image:
                images.append(image)
            else:
                remaining_file_ids.append(file_id)
        
        # Update filters with remaining file IDs
        updated_filters = None
        
        if remaining_file_ids:
            updated_filters = effective_filters.copy()
            # Preserve original document_id format
            if isinstance(document_ids, dict):
                updated_filters["document_id"] = {"$in": remaining_file_ids}
            else:  # isinstance(document_ids, list)
                updated_filters["document_id"] = remaining_file_ids
        else:
            # No remaining files, disable knowledge search
            self.search_knowledge = False
        
        return images, updated_filters

    async def _get_image_file(self, file_id: str) -> Optional[Image]:
        """Get image file by ID and return as Image object"""
        try:
            file = await File.find_one(File.id == ObjectId(file_id))

            if not file:
                print(f"File not found: {file_id}")
                return None
            
            # Check if file is an image
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
            file_extension = file.name.lower().split('.')[-1] if '.' in file.name else ''
            if f'.{file_extension}' not in image_extensions:
                print(f"File {file.name} is not an image")
                return None
            
            # Get image content from blob storage
            image_content = await AzureBlobService().get_blob(file.key)

            # Create Image object
            return Image(
                content=image_content,
                name=file.name
            )
        except Exception as e:
            print(f"Error retrieving image file {file_id}: {e}")
            return None
