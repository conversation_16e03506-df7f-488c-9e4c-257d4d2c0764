from typing import Any, Dict, Optional


def create_summarizer(knowledge_base, filters: Optional[Dict[str, Any]] = None):
    async def summarize_all_ur(query: str) -> str:
        """Summarize all user requirements of provided file by get all documents matching the query in the knowledge base.

        Args:
            query (str): The query string to search for relevant documents.
        """
        documents = await knowledge_base.async_search(
            query=query, num_documents=50, filters=filters  # Get all relevant documents
        )

        sorted_docs = sorted(
            documents, key=lambda x: (x.meta_data.get("document_id", ""), x.meta_data.get("chunk", 0)), reverse=True
        )

        formatted_docs = []
        for doc in sorted_docs:
            formatted_doc = f"Document Name: {doc.meta_data.get('name', '')}, Chunk order: {doc.meta_data.get('chunk', 0)}\nContent: {doc.content}\n"
            formatted_docs.append(formatted_doc)
        return "\n---\n".join(formatted_docs)

    return summarize_all_ur
