from agno.agent import Agent

from app.config import APP_CONFIG
from app.factories.chat_factory import create_chat_model
from app.schema import ArtefactParsedData, ArtifactParseRequest

__TEMPLATE = """
<objective>
Accurately summarize and extract only the content relevant to the user request from the parsing content, without adding or creating any new information. Present the result strictly following the defined output format.
</objective>
<rule>
- DON'T BE CREATIVE, DO NOT ADD OR CREATE ANY NEW INFORMATION.
- The parsing content may include more information than the user request requires; extract only what matches the user request.
- If the parsing content lacks sufficient information to fulfill the user request, do not make any assumptions.
- OUTPUT MUST REFER TO THE EXACT CONTENT IN THE PARSING CONTENT.
- OUTPUT MUST INCLUDE ARTEFACTS IN CONTEXT ONLY IF THEY ARE EXPLICITLY MENTIONED IN THE PARSING CONTENT.
- EXTRACT CONTENT, DO NOT GENERATE ANYTHING.
</rule>
<request>
{user_request}
</request>
<context>
{context}
</context>
"""


async def parse_data_from_message(request: ArtifactParseRequest,
                                  model_id=APP_CONFIG.common_chat_model) -> ArtefactParsedData:
    model = create_chat_model(APP_CONFIG.chat_model_config, model_id=model_id)
    agent = Agent(
        model=model,
        name="Data Parser",
        response_model=ArtefactParsedData,
        system_message=__TEMPLATE.format(user_request=request.user_request, context=request.context),
    )
    response = await agent.arun(message=f"Parse the following content: <content>{request.content}</content>")
    return response.content
