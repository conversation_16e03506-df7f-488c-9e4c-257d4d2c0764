import os
from typing import Optional

import httpx
from agno.tools import Toolkit
from agno.utils.log import logger


class ArtefactsFindingTool(Toolkit):
    def __init__(
            self,
            api_key: Optional[str] = None,
            base_url: Optional[str] = None,
            **kwargs,
    ):
        super().__init__(name="artefacts_tools", **kwargs)

        self.api_key = api_key or os.getenv("ORCA_API_KEY")
        self.base_url = base_url or os.getenv("ORCA_BASE_URL", "https://orca-backend.ops-ai.dev")
        if not self.api_key:
            logger.error("No Orca API key provided")
        self.register(self.filter_artefacts)
        self.register(self.search_artefacts)

    def build_header(self):
        headers = {
            "accept": "*/*",
            "access-control-allow-origin": "*",
            "authentication": f"Bearer {self.api_key}",
            "authorization": f"Bearer {self.api_key}"
        }
        return headers

    def filter_artefacts(self, artefact_id: str, project_id: str = "RTMVP") -> dict:
        """
        Fetch artefact details from the Orca backend API.

        Parameters:
            artefact_id (str): The ID artefact of the object to retrieve
            project_id (str): The project ID (defaults to RTMVP)

        Returns:
            str: A message containing the fetched artefact data, or an error message if the fetch failed
        """

        # Check if artefact_id is a string like 'USR0126'
        if artefact_id.isalpha() or artefact_id.isalnum():
            logger.info("Artefact ID appears to be a string. Searching for artefact ID...")
            search_result = self.search_artefacts(project_id=project_id)

            if search_result["status"] == "success":
                logger.info("Artefact ID found in search results.")
                artefacts = search_result["data"]
                matching_artefact = next((item for item in artefacts if item["artefact_code"] == artefact_id), None)

                if matching_artefact:
                    artefact_id = matching_artefact["artefact_id"]
                else:
                    error_msg = f"Artefact ID {artefact_id} not found in search results."
                    logger.error(error_msg)
            else:
                logger.error("Failed to fetch artefact data during search.")
                return search_result["data"]

        # Proceed with the request if artefact_id is an integer or valid string
        url = f"{self.base_url}/api/projects/{project_id}/UserRequirements/{artefact_id}"

        try:
            response = httpx.get(url, headers=self.build_header())
            response.raise_for_status()

            data = response.json()
            return {"status": "success", "data": data}

        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP error occurred: {e.response.status_code} - {e.response.text}"
            logger.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"An error occurred: {e}"
            logger.error(error_msg)
            return {"status": "error", "data": error_msg}

    def search_artefacts(self, project_id: str = "RTMVP") -> dict:
        """
        Search and fetch the list of artefacts from the Orca backend API.

        Parameters:
            project_id (str): The project ID (defaults to RTMVP)

        Returns:
            dict: A dictionary containing the status and data of the fetched artefacts.

        The response is transformed from the original format to:
            {
                "status": "success" or "error",
                "data": [
                    {
                        "artefact_id": "The ID artefact. Uses this id as parameter to filter_artefacts tool",
                        "artefact_code": "code of artefact",
                        "title": "the title of artefact",
                        "artefact_type": "the type of artefact",
                        "description": "description of the artefact"
                    }
                ]
            }
        """

        url = f"{self.base_url}/api/projects/{project_id}/Mentions"

        try:
            response = httpx.get(url, headers=self.build_header())
            response.raise_for_status()

            original_data = response.json()
            transformed_data = [
                {
                    "artefact_id": item["name"],
                    "artefact_code": item["code"],
                    "title": item["id"],
                    "artefact_type": item["artefactType"],
                    "description": item["description"]
                }
                for item in original_data
            ]
            return {"status": "success", "data": transformed_data}

        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP error occurred: {e.response.status_code} - {e.response.text}"
            logger.error(error_msg)
            return {"status": "error", "data": error_msg}
        except Exception as e:
            error_msg = f"An error occurred: {e}"
            logger.error(error_msg)
            return {"status": "error", "data": error_msg}
