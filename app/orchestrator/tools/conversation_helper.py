from agno.agent import Agent

from app.config import APP_CONFIG
from app.factories.chat_factory import create_chat_model


async def generate_conversation_name(first_message_content: str, model_id=APP_CONFIG.common_chat_model) -> str:
    model = create_chat_model(APP_CONFIG.chat_model_config, model_id=model_id)
    agent = Agent(model=model, name="Conversation Name Generator", markdown=False)
    response = await agent.arun(
        message=f"Generates a concise (under 20 words) and descriptive name for a conversation based on this message content: {first_message_content}"
    )
    return response.content.strip('"')
