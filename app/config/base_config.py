"""Define the configurable parameters for the agent."""

from __future__ import annotations

from typing import Dict, List, Literal, Optional, Type, TypeVar, Union, cast

from dotenv import load_dotenv
from pydantic import BaseModel, Field, SecretStr, model_validator
from typing_extensions import Self

from app.utils.get_env import from_env, secret_from_env

from .config_loader import CONFIG

load_dotenv(override=True)


class AuthConfig(BaseModel):
    jwt_jwks_uri: str = Field(default_factory=from_env("JWT_JWKS_URI"), description="JWKS URI for JWT verification")
    jwt_issuer: str = Field(
        default_factory=from_env("JWT_ISSUER", default=""), description="JWT issuer for JWT verification"
    )
    jwt_audience: Optional[str] = Field(
        default_factory=from_env("AUDIENCE", default=""), description="JWT audience for JWT verification"
    )


class ExternalApiConfig(BaseModel):
    orca_backend_url: str = Field(
        default_factory=from_env("ORCA_BACKEND_URL", default="https://orca-backend.ops-ai.dev"),
        description="Base URL for ORCA backend API"
    )


class FileConfig(BaseModel):
    blob_account_name: str = Field(
        default_factory=from_env("BLOB_ACCOUNT_NAME", default=""), description="Azure Blob Storage account name"
    )
    blob_account_key: str = Field(
        default_factory=from_env("BLOB_ACCOUNT_KEY", default=""), description="Azure Blob Storage account key"
    )
    blob_container_name: str = Field(
        default_factory=from_env("BLOB_CONTAINER_NAME", default="files"),
    )
    max_file_size: int = (Field(default_factory=lambda: int(from_env("MAX_FILE_SIZE", default="********")())),)
    pre_signed_url_expiry: int = Field(default_factory=lambda: int(from_env("PRESIGNED_URL_EXPIRY", default="3600")()))


class PhoenixConfig(BaseModel):
    project_id: Optional[str] = Field(
        default_factory=from_env("PHOENIX_PROJECT_ID", default="default"),
        description="Phoenix project ID for the agent",
    )
    api_key: SecretStr = Field(
        default_factory=secret_from_env("PHOENIX_API_KEY", default=""), description="API key for Phoenix integration"
    )
    endpoint: str = Field(
        default_factory=from_env("PHOENIX_ENDPOINT", default="https://api.phoenix.agno.com"),
        description="Phoenix API URL",
    )
    enabled: str = Field(
        default_factory=from_env("PHOENIX_ENABLED", default="no"),
        description="Enable or disable Phoenix integration",
    )
    kwargs: Dict = Field(default={})


class AzureOpenAIConfig(BaseModel):
    api_key: SecretStr = Field(default_factory=secret_from_env("AZURE_OPENAI_API_KEY"))
    azure_endpoint: str = Field(default_factory=from_env("AZURE_OPENAI_ENDPOINT"))
    api_version: str = Field(default_factory=from_env("AZURE_OPENAI_API_VERSION"))
    azure_deployment: Optional[str] = None
    kwargs: Dict = Field(default={})


class MongoDBConfig(BaseModel):
    url: str = Field(default_factory=from_env("MONGO_URL"))
    database_name: str = Field(default_factory=from_env("MONGODB_DATABASE_NAME"))


class VectorDBConfig(BaseModel):
    url: str = Field(default_factory=from_env("QDRANT_URL", default="http://localhost:6333"))
    api_key: Optional[str] = Field(default_factory=from_env("QDRANT_API_KEY", default=""))
    collection: str = Field(default_factory=from_env("QDRANT_COLLECTION", default="vectordb"))


class KnowledgeBaseConfig(BaseModel):
    search_type: Literal["vector", "keyword", "hybrid"] = Field(
        default_factory=lambda: CONFIG.get("knowledge_base_config", {}).get("search_type", "vector")
    )
    num_documents: int = Field(default_factory=lambda: CONFIG.get("knowledge_base_config", {}).get("num_documents", 5))
    chunk_size: int = Field(default_factory=lambda: CONFIG.get("knowledge_base_config", {}).get("chunk_size", 1000))
    chunk_overlap: int = Field(
        default_factory=lambda: CONFIG.get("knowledge_base_config", {}).get("chunk_overlap", 200)
    )


class BaseConfiguration(BaseModel):
    environment: str = Field(default_factory=from_env("ENVIRONMENT", default="development"))
    requirement_tool_uri: str = Field(
        default_factory=from_env("REQUIREMENT_TOOL_URI", default="http://localhost:8000/api")
    )
    chat_model_config: Union[AzureOpenAIConfig] = AzureOpenAIConfig()
    embedding_model_config: Union[AzureOpenAIConfig] = AzureOpenAIConfig()
    mongo_config: Union[MongoDBConfig] = MongoDBConfig()
    vectordb_config: VectorDBConfig = VectorDBConfig()
    knowledge_base_config: KnowledgeBaseConfig = KnowledgeBaseConfig()
    auth_config: Union[AuthConfig] = AuthConfig()
    external_api_config: ExternalApiConfig = ExternalApiConfig()
    phoenix_config: PhoenixConfig = PhoenixConfig()
    file_config: FileConfig = FileConfig()
    available_chat_models: List[str] = Field(default_factory=lambda: CONFIG.get("available_chat_models", []))
    common_chat_model: str = Field(default_factory=lambda: CONFIG.get("common_chat_model", "gpt-4o"))
    cost_per_1m_prompt_tokens: Dict[str, float] = Field(
        default_factory=lambda: CONFIG.get("cost_per_1m_prompt_tokens", {})
    )
    cost_per_1m_completion_tokens: Dict[str, float] = Field(
        default_factory=lambda: CONFIG.get("cost_per_1m_completion_tokens", {})
    )
    cost_per_1m_cached_tokens: Dict[str, float] = Field(
        default_factory=lambda: CONFIG.get("cost_per_1m_cached_tokens", {})
    )

    @model_validator(mode="after")
    def validate_provider(self) -> Self:
        self.embedding_model_config.azure_deployment = from_env("AZURE_EMBEDER_DEPLOYMENT")()

        return self

    @classmethod
    def from_runnable_config(cls: Type[T], config: Optional[Dict] = None) -> T:
        """Create an IndexConfiguration instance from a RunnableConfig object.

        Args:
            cls (Type[T]): The class itself.
            config (Optional[RunnableConfig]): The configuration object to use.

        Returns:
            T: An instance of IndexConfiguration with the specified configuration.
        """
        return cast(T, update_config(config, cls))


def update_config(val, cls):
    if isinstance(val, Dict) and not isinstance(val, cls):
        _fields = {
            f_name: f_info.annotation
            for f_name, f_info in cls.model_fields.items()
            if f_info.init or f_info.init is None
        }
        return cls(**{k: update_config(v, _fields[k]) for k, v in val.items() if k in _fields.keys()})
    return val


APP_CONFIG = BaseConfiguration()
T = TypeVar("T", bound=BaseConfiguration)
