import time

from app.config.base_config import BaseConfiguration

default_ttl_seconds = 300  # 5 minutes
_config_instance = None
_config_timestamp = 0


def get_config(ttl_seconds: float = default_ttl_seconds):
    global _config_instance, _config_timestamp
    now = time.time()
    if _config_instance is None or (now - _config_timestamp) > ttl_seconds:
        from dotenv import load_dotenv

        load_dotenv(override=True)
        _config_instance = BaseConfiguration()
        _config_timestamp = int(now)
    return _config_instance
