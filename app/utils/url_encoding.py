"""
URL encoding utilities for project codes.
This module provides helper functions for encoding/decoding project codes for use in URLs.
"""

import urllib.parse


def encode_project_code(project_code: str) -> str:
    """
    Encode a project code for use in URLs.
    
    Args:
        project_code: The project code to encode (e.g., "My Project")
        
    Returns:
        str: URL-encoded project code (e.g., "My%20Project")
    
    Example:
        >>> encode_project_code("My Project")
        'My%20Project'
        >>> encode_project_code("RTMVP")
        'RTMVP'
    """
    return urllib.parse.quote(project_code, safe='')


def decode_project_code(encoded_project_code: str) -> str:
    """
    Decode a URL-encoded project code.
    
    Args:
        encoded_project_code: The URL-encoded project code (e.g., "My%20Project")
        
    Returns:
        str: Decoded project code (e.g., "My Project")
    
    Example:
        >>> decode_project_code("My%20Project")
        'My Project'
        >>> decode_project_code("RTMVP")
        'RTMVP'
    """
    return urllib.parse.unquote(encoded_project_code)


def is_project_code_encoded(project_code: str) -> bool:
    """
    Check if a project code appears to be URL-encoded.
    
    Args:
        project_code: The project code to check
        
    Returns:
        bool: True if the project code appears to be URL-encoded, False otherwise
    
    Example:
        >>> is_project_code_encoded("My%20Project")
        True
        >>> is_project_code_encoded("RTMVP")
        False
    """
    return '%' in project_code


def normalize_project_code_for_url(project_code: str) -> str:
    """
    Normalize a project code for use in URLs.
    If already encoded, returns as-is. If not encoded, encodes it.
    
    Args:
        project_code: The project code to normalize
        
    Returns:
        str: URL-safe project code
    
    Example:
        >>> normalize_project_code_for_url("My Project")
        'My%20Project'
        >>> normalize_project_code_for_url("My%20Project")
        'My%20Project'
    """
    if is_project_code_encoded(project_code):
        return project_code
    return encode_project_code(project_code)
