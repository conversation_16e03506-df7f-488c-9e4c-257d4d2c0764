from openinference.instrumentation.agno import AgnoInstrumentor
from phoenix.otel import register

from app.config import APP_CONFIG


def agno_tracing_setup() -> None:
    tracer_provider = register(
        project_name=APP_CONFIG.phoenix_config.project_id,
        endpoint=APP_CONFIG.phoenix_config.endpoint,
        batch=True,
        set_global_tracer_provider=False,
    )
    AgnoInstrumentor().instrument(tracer_provider=tracer_provider)
