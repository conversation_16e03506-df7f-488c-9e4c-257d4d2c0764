from typing import Any, Dict, Optional

from app.schema import AgentSchema


class AgentConfigHelper:
    """Simple helper class for common agent configuration operations"""

    @staticmethod
    def safe_get(obj, attr: str, default=None):
        """Safely get attribute from object with fallback"""
        try:
            return getattr(obj, attr, default) if obj else default
        except AttributeError:
            return default

    @staticmethod
    def safe_get_nested(obj, path: str, default=None):
        """Safely get nested attribute using dot notation"""
        try:
            result = obj
            for attr in path.split("."):
                result = getattr(result, attr, None)
                if result is None:
                    return default
            return result if result is not None else default
        except AttributeError:
            return default

    @staticmethod
    def extract_config(agent_config: Optional[AgentSchema], defaults: Dict[str, Any]) -> Dict[str, Any]:
        """Extract configuration with defaults"""
        if not agent_config:
            return defaults

        return {
            "model_id": AgentConfigHelper.safe_get(agent_config, "model_id", defaults.get("model_id", "gpt-4o")),
            "name": AgentConfigHelper.safe_get(agent_config, "name", defaults.get("name", "Unknown Agent")),
            "role": AgentConfigHelper.safe_get(agent_config, "role", defaults.get("role", "Unknown Role")),
            "description": AgentConfigHelper.safe_get(agent_config, "description", defaults.get("description", "")),
            "tools": AgentConfigHelper.safe_get(agent_config, "tools", defaults.get("tools", [])),
            "system_prompt": AgentConfigHelper.safe_get_nested(
                agent_config, "system_prompt", defaults.get("system_prompt", "")
            ),
            "additional_prompt": AgentConfigHelper.safe_get_nested(
                agent_config, "additional_prompt", defaults.get("additional_prompt", "")
            ),
        }

    @staticmethod
    def merge_instructions(base_instructions: str, additional_instructions: str = None) -> str:
        """Merge instructions and additional_instruction into combined instructions"""
        if additional_instructions:
            return f"{base_instructions}\n\n# ADDITIONAL INSTRUCTION FROM USER\n{additional_instructions}"
        return base_instructions

    @staticmethod
    def enhance_instructions(
        instructions: str, additional_instruction: str, tools: list, role: str, description: str
    ) -> str:
        """Enhance instructions with additional context and metadata"""
        enhanced = instructions

        if additional_instruction:
            enhanced = f"{enhanced}\n\nAdditional Context:\n{additional_instruction}"

        if tools:
            enhanced += f"\n\nAvailable Tools: {', '.join(tools)}"

        enhanced += f"\n\nYour role: {role}"
        if description:
            enhanced += f"\nDescription: {description}"

        return enhanced

    @staticmethod
    def log_agent_init(agent_type: str, agent_id: str, config: Dict[str, Any], has_db_config: bool):
        """Log agent initialization"""
        config_source = "database config" if has_db_config else "default config"
        print(f"\n=== {agent_type} Initialization ===")
        print(f"Agent ID: {agent_id}")
        print(f"Config source: {config_source}")
        print(f"Model ID: {config['model_id']}")
        print(f"Name: {config['name']}")
        print(f"Role: {config['role']}")
        instructions = config["system_prompt"]
        print(f"Instructions: {instructions[:100]}..." if len(instructions) > 100 else f"Instructions: {instructions}")
        print("=" * (len(agent_type) + 20))
