from collections.abc import Sequence
import os
from typing import Any, Callable, Optional, Union

from pydantic import SecretStr


class _NoDefaultType:
    """Type to indicate no default value is provided."""


_NoDefault = _NoDefaultType()


def from_env(
    key: Union[str, Sequence[str]],
    /,
    *,
    default: Union[str, _NoDefaultType] = _NoDefault,
    error_message: Optional[str] = None,
) -> Union[Callable[[], str]]:
    """Create a factory method that gets a value from an environment variable.

    Args:
        key: The environment variable to look up. If a list of keys is provided,
            the first key found in the environment will be used.
            If no key is found, the default value will be used if set,
            otherwise an error will be raised.
        default: The default value to return if the environment variable is not set.
        error_message: the error message which will be raised if the key is not found
            and no default value is provided.
            This will be raised as a ValueError.
    """

    def get_from_env_fn() -> str:
        """Get a value from an environment variable."""
        if isinstance(key, (list, tuple)):
            for k in key:
                if k in os.environ:
                    return os.environ[k]
        if isinstance(key, str) and key in os.environ:
            return os.environ[key]

        if isinstance(default, (str, type(None))):
            return default
        if error_message:
            raise ValueError(error_message)
        msg = (
            f"Did not find {key}, please add an environment variable"
            f" `{key}` which contains it, or pass"
            f" `{key}` as a named parameter."
        )
        raise ValueError(msg)

    return get_from_env_fn


def secret_from_env(
    key: Union[str, Sequence[str]],
    /,
    *,
    default: Union[str, _NoDefaultType] = _NoDefault,
    error_message: Optional[str] = None,
) -> Union[Callable[[], SecretStr]]:
    """Secret from env.

    Args:
        key: The environment variable to look up.
        default: The default value to return if the environment variable is not set.
        error_message: the error message which will be raised if the key is not found
            and no default value is provided.
            This will be raised as a ValueError.

    Returns:
        factory method that will look up the secret from the environment.
    """

    def get_secret_from_env() -> SecretStr:
        """Get a value from an environment variable."""
        if isinstance(key, (list, tuple)):
            for k in key:
                if k in os.environ:
                    return SecretStr(os.environ[k])
        if isinstance(key, str) and key in os.environ:
            return SecretStr(os.environ[key])
        if isinstance(default, str):
            return SecretStr(default)
        if default is None:
            return None
        if error_message:
            raise ValueError(error_message)
        msg = (
            f"Did not find {key}, please add an environment variable"
            f" `{key}` which contains it, or pass"
            f" `{key}` as a named parameter."
        )
        raise ValueError(msg)

    return get_secret_from_env


def get_value_from_dict(
    key: Union[str, Sequence[str]], data: dict, /, *, default: Optional[Any] = _NoDefault
) -> Callable:
    """Create a factory method that gets a value from a dictionary by a key path.

    Args:
        key: The key path to look up, either as a string or a list of keys.
            If a list of keys is provided, the first key found will be used.
        data: The dictionary in which the key(s) are searched.
    """

    def get_value_fn():
        """Retrieve a value from a dictionary based on a key path."""
        keys = key.split(".") if isinstance(key, str) else key
        res = data
        for k in keys:
            try:
                res = res[k]
            except KeyError:
                if default is _NoDefault:
                    msg = f"Did not find {keys} ({k} not found), please add an correct keys."
                    raise KeyError(msg)
                else:
                    return default
        return res

    return get_value_fn
