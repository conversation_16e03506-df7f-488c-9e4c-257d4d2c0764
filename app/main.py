from contextlib import asynccontextmanager
from http.client import <PERSON><PERSON><PERSON><PERSON>x<PERSON>

from beanie import init_beanie
from fastapi import Fast<PERSON><PERSON>, status
from starlette.middleware.cors import CORSMiddleware

from app.api import api_settings, health_router, v1_router
from app.config import APP_CONFIG
from app.db import close_mongo_connection, connect_to_mongo, get_mongo_database
from app.exception.handler import exception_handler, not_found_exception_handler
from app.model import app_models

if APP_CONFIG.phoenix_config.enabled.lower() in ["true", "yes", "y", "1"]:
    from app.utils.trace import agno_tracing_setup

    agno_tracing_setup()


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """
    Lifespan context manager for FastAPI app.
    Handles startup and shutdown operations.
    """
    # Startup
    try:
        await connect_to_mongo()
        await init_beanie(database=get_mongo_database(), document_models=app_models)
        yield
    finally:
        # Shutdown
        await close_mongo_connection()


def create_app() -> FastAPI:
    """Create a FastAPI App"""

    # Create FastAPI App with lifespan
    _app: FastAPI = FastAPI(
        title=api_settings.title,
        version=api_settings.version,
        docs_url="/docs" if api_settings.docs_enabled else None,
        redoc_url="/redoc" if api_settings.docs_enabled else None,
        openapi_url="/openapi.json" if api_settings.docs_enabled else None,
        lifespan=lifespan,
    )

    # Add v1 router
    _app.include_router(v1_router)
    _app.include_router(health_router)

    # Add Middlewares
    _app.add_middleware(
        CORSMiddleware,
        allow_origins=api_settings.cors_origin_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    _app.add_exception_handler(HTTPException, exception_handler)
    _app.add_exception_handler(status.HTTP_500_INTERNAL_SERVER_ERROR, exception_handler)
    _app.add_exception_handler(status.HTTP_404_NOT_FOUND, not_found_exception_handler)

    return _app


# Create a FastAPI app
app = create_app()

if __name__ == "__main__":
    import uvicorn

    # Start the server
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True, log_level="debug")
