"""
Project-based authentication dependencies for FastAPI routes.
This module provides authentication and authorization for project-specific endpoints.
"""

import urllib.parse
from typing import List, Optional
from fastapi import Depends, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer

from app.service.external_api import ExternalApi<PERSON>lient, UserInfo
from pkg.auth.jwt import get_token


class ProjectAuth:
    """Authentication and authorization for project-specific endpoints"""
    
    def __init__(self):
        self.external_api = ExternalApiClient()
        self.security = HTTPBearer()
    
    async def get_user_info(self, request: Request) -> UserInfo:
        """
        Get user information from external API using JWT token
        
        Args:
            request: FastAPI request object containing Authorization header
            
        Returns:
            UserInfo: User information from external API
            
        Raises:
            HTTPException: If authentication fails
        """
        token = get_token(request)
        if not token:
            raise HTTPException(status_code=401, detail="No authorization token provided")
        
        return await self.external_api.get_user_info(token)
    
    async def validate_project_access(self, project_code: str, request: Request) -> UserInfo:
        """
        Validate user has access to specific project
        
        Args:
            project_code: URL-encoded project code to validate access for
            request: FastAPI request object
            
        Returns:
            UserInfo: User information if access is valid
            
        Raises:
            HTTPException: If user doesn't have access to project
        """
        # Decode the URL-encoded project code
        decoded_project_code = urllib.parse.unquote(project_code)
        
        user_info = await self.get_user_info(request)
        
        # Check if user has access to the project (case-insensitive comparison)
        has_access = any(
            project.project_code.lower() == decoded_project_code.lower()
            for project in user_info.projects
        )
        
        if not has_access:
            raise HTTPException(
                status_code=403, 
                detail=f"Access denied to project '{decoded_project_code}'"
            )
        
        return user_info
    
    async def get_user_project_roles(self, project_code: str, request: Request) -> List[str]:
        """
        Get user roles for specific project
        
        Args:
            project_code: URL-encoded project code to get roles for
            request: FastAPI request object
            
        Returns:
            List[str]: List of user roles in the project
            
        Raises:
            HTTPException: If user doesn't have access to project
        """
        # Decode the URL-encoded project code
        decoded_project_code = urllib.parse.unquote(project_code)
        
        user_info = await self.validate_project_access(project_code, request)
        
        for project in user_info.projects:
            if project.project_code.lower() == decoded_project_code.lower():
                return project.roles
        
        # This shouldn't happen since validate_project_access passed
        raise HTTPException(status_code=403, detail=f"No roles found for project '{decoded_project_code}'")


# Create a singleton instance
project_auth = ProjectAuth()


# Dependency functions for FastAPI
async def get_authenticated_user(request: Request) -> UserInfo:
    """FastAPI dependency to get authenticated user info"""
    return await project_auth.get_user_info(request)


def require_project_access(project_code: str):
    """
    FastAPI dependency factory to require access to specific project
    
    Args:
        project_code: Project code to require access for
        
    Returns:
        Dependency function that validates project access
    """
    async def _validate_access(request: Request) -> UserInfo:
        return await project_auth.validate_project_access(project_code, request)
    
    return _validate_access


def get_project_user_roles(project_code: str):
    """
    FastAPI dependency factory to get user roles for specific project
    
    Args:
        project_code: Project code to get roles for
        
    Returns:
        Dependency function that returns user roles
    """
    async def _get_roles(request: Request) -> List[str]:
        return await project_auth.get_user_project_roles(project_code, request)
    
    return _get_roles


# Dynamic dependency for path parameters
async def validate_project_access_from_path(project_code: str, request: Request) -> UserInfo:
    """
    FastAPI dependency to validate project access using path parameter
    
    Args:
        project_code: Project code from path parameter
        request: FastAPI request object
        
    Returns:
        UserInfo: User information if access is valid
    """
    return await project_auth.validate_project_access(project_code, request)
