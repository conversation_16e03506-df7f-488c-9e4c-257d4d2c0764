from typing import Any

from fastapi import Depends

from app.config.base_config import APP_CONFIG
from pkg.auth import J<PERSON><PERSON><PERSON><PERSON><PERSON>, User
from pkg.exception import HttpException

_config = APP_CONFIG.auth_config
jwt = JWTBearer(
    jwks_uri=_config.jwt_jwks_uri, issuer=_config.jwt_issuer, audience=_config.jwt_audience, is_verify=False
)


def get_user_id(user: User = Depends(jwt)):
    return user.sub


def get_creator_authorize(user_id: str = Depends(get_user_id)):
    def _compare_with_user_id(entity, creator_attribute):
        if not hasattr(entity, creator_attribute):
            return False
        return getattr(entity, creator_attribute) == user_id

    def _authorize(entity: Any, creator_attribute="user_id"):
        if not _compare_with_user_id(entity, creator_attribute):
            raise HttpException.forbidden()

    return _authorize
