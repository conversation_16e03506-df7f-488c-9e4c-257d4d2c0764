import logging

from fastapi import Request
from starlette.exceptions import HTTPException as StarletteHTTPException

from pkg.exception.http_exception import HttpException
from pkg.response import HttpResponse

logger = logging.getLogger(__name__)


def exception_handler(request: Request, ex: Exception):
    logger.exception(ex)

    if isinstance(ex, StarletteHTTPException) and not isinstance(ex, HttpException):
        ex = HttpException.from_starlette_exception(ex)
    if not isinstance(ex, HttpException):
        ex = HttpException.internal_server_error()
    ex.metadata["path"] = request.url.path
    return HttpResponse.from_exception(ex)


def not_found_exception_handler(request: Request, ex: Exception):
    return exception_handler(request, ex if isinstance(ex, HttpException) else HttpException.not_found())
