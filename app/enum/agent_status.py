from enum import IntEnum
from typing import Dict, Any


class AgentStatus(IntEnum):
    """Agent status enum with custom numeric values for frontend"""
    DRAFT = 0
    SUBMITTED = 1
    APPROVED = 7
    
    def __str__(self):
        return self.name.lower()
    
    @classmethod
    def from_int(cls, value: int) -> 'AgentStatus':
        """Convert integer value to AgentStatus enum"""
        for status in cls:
            if status.value == value:
                return status
        raise ValueError(f"Invalid agent status value: {value}")
    
    @classmethod
    def from_string(cls, value: str) -> 'AgentStatus':
        """Convert string value to AgentStatus enum"""
        value_upper = value.upper()
        for status in cls:
            if status.name == value_upper:
                return status
        raise ValueError(f"Invalid agent status string: {value}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "value": self.value,
            "name": self.name.lower(),
            "display_name": self.name.replace("_", " ").title()
        }
