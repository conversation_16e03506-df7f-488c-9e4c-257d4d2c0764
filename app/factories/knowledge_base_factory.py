"""Knowledge base factory for creating MarkdownKnowledgeBase instances"""

import types
from typing import Any, Dict, Optional
from qdrant_client.http import models
from agno.document.chunking.markdown import MarkdownChunking
from agno.knowledge.markdown import MarkdownKnowledgeBase
from agno.vectordb.qdrant import Qdrant

from app.config.get_config import get_config
from app.factories.embedding_factory import create_embedding_model
from app.schema.chunk_metadata import ChunkMetadata


def _create_indexes_for_new_collection(vector_db: Qdrant, collection_name: str) -> None:
    """
    Create indexes when collection is first created
    
    Args:
        vector_db: Qdrant vectordb instance
        collection_name: Name of the collection
    """
    try:
        # Generate required fields from ChunkMetadata schema
        required_fields = [f"meta_data.{field_name}" for field_name in ChunkMetadata.model_fields.keys()]
        
        # Create indexes for all required fields
        for field in required_fields:
            try:
                vector_db.client.create_payload_index(
                    collection_name=collection_name,
                    field_name=field,
                    field_schema=models.PayloadSchemaType.KEYWORD
                )
            except Exception:
                # Index creation failed, but continue
                pass
                
    except Exception as e:
        # Log error but don't fail
        print(f"Warning: Could not create indexes: {e}")


def _ensure_collection_with_indexes(vector_db: Qdrant, collection_name: str) -> None:
    """
    Ensure collection exists and create indexes only if collection is new
    
    Args:
        vector_db: Qdrant vectordb instance
        collection_name: Name of the collection
    """
    try:
        # Check if collection exists
        collection_exists = vector_db.exists()
        
        if not collection_exists:
            # Create collection
            vector_db.create()
            # Create indexes only for new collection
            _create_indexes_for_new_collection(vector_db, collection_name)
                
    except Exception as e:
        print(f"Warning: Could not ensure collection with indexes: {e}")


def create_knowledge_base() -> MarkdownKnowledgeBase:
    """
    Create MarkdownKnowledgeBase following the processor pattern

    Returns:
        MarkdownKnowledgeBase: Configured knowledge base instance
    """
    config = get_config()

    # Create chunking strategy with configurable parameters
    chunking_strategy = MarkdownChunking(
        chunk_size=config.knowledge_base_config.chunk_size, overlap=config.knowledge_base_config.chunk_overlap
    )

    # Create MongoDB vectordb using vectordb config
    vector_db = create_vectordb()

    # Create MarkdownKnowledgeBase with vectordb and chunking strategy
    knowledge_base = MarkdownKnowledgeBase(
        vector_db=vector_db,
        chunking_strategy=chunking_strategy,
        num_documents=config.knowledge_base_config.num_documents,
        valid_metadata_filters=set(ChunkMetadata.model_fields.keys()),
    )

    return knowledge_base


def create_vectordb(collection: Optional[str] = None) -> Qdrant:
    """
    Create Qdrant vectordb instance for use in processors

    Args:
        collection: Optional collection name, uses config default if not provided

    Returns:
        Qdrant: Configured vectordb instance
    """
    config = get_config()

    # Create embedder using the same factory as processor
    embedder = create_embedding_model(config.embedding_model_config)

    def _format_filters(self, filters: Optional[Dict[str, Any]]) -> Optional[models.Filter]:
        if filters:
            filter_conditions = []
            for key, value in filters.items():
                # If key contains a dot already, assume it's in the correct format
                # Otherwise, assume it's a metadata field and add the prefix
                if "." not in key and not key.startswith("meta_data."):
                    # This is a simple field name, assume it's metadata
                    key = f"meta_data.{key}"

                if isinstance(value, dict):
                    # Handle nested dictionaries
                    for sub_key, sub_value in value.items():
                        full_key = f"{key}.{sub_key}"
                        
                        # Handle lists in nested values
                        if isinstance(sub_value, (list, tuple)) and sub_value:
                            if len(sub_value) == 1:
                                filter_conditions.append(
                                    models.FieldCondition(key=full_key, match=models.MatchValue(value=sub_value[0]))
                                )
                            else:
                                filter_conditions.append(
                                    models.FieldCondition(key=full_key, match=models.MatchAny(any=sub_value))
                                )
                        elif sub_value is not None:
                            filter_conditions.append(
                                models.FieldCondition(key=full_key, match=models.MatchValue(value=sub_value))
                            )
                else:
                    # Handle direct key-value pairs
                    if isinstance(value, (list, tuple)) and value:
                        # Handle lists - use MatchAny for multiple values, MatchValue for single
                        if len(value) == 1:
                            filter_conditions.append(
                                models.FieldCondition(key=key, match=models.MatchValue(value=value[0]))
                            )
                        else:
                            filter_conditions.append(
                                models.FieldCondition(key=key, match=models.MatchAny(any=value))
                            )
                    elif value is not None:
                        # Handle single values
                        filter_conditions.append(
                            models.FieldCondition(key=key, match=models.MatchValue(value=value))
                        )

            if filter_conditions:
                return models.Filter(must=filter_conditions)

        return None

    # Use provided collection name or default from config
    collection_name = collection or config.vectordb_config.collection
    
    # Create Qdrant vectordb using vectordb config
    vector_db = Qdrant(
        collection=collection_name,
        url=config.vectordb_config.url,
        api_key=config.vectordb_config.api_key,
        embedder=embedder,
    )

    vector_db._format_filters = types.MethodType(_format_filters, vector_db)
    
    # Ensure collection exists and create indexes only if new
    _ensure_collection_with_indexes(vector_db, collection_name)

    return vector_db
