import logging
from typing import List, Optional

from fastapi import Depends

from app.constant import <PERSON><PERSON><PERSON>
from app.orchestrator.agents.hlr_agent import HLRAgent
from app.orchestrator.agents.master_agent import MasterAgent
from app.orchestrator.agents.ur_agent import URAgent
from app.orchestrator.agents.usecase_agent import UseCaseAgent
from app.orchestrator.agents.screen_agent import ScreenAgent
from app.service import AgentService

logger = logging.getLogger(__name__)


class AgentFactory:
    AGENT_CLASSES = {
        AgentCode.UserRequirement: URAgent,
        AgentCode.HighLevelRequirement: HLRAgent,
        AgentCode.Master: MasterAgent,
        AgentCode.UseCase: UseCaseAgent,
        AgentCode.Screen: ScreenAgent
    }

    def __init__(self, agent_service: AgentService = Depends(AgentService)):
        self.agent_service = agent_service

    async def create_agent(
        self,
        agent_code: AgentCode,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        debug_mode: bool = True,
        file_ids: Optional[List[str]] = None,
        project: Optional[str] = None,
    ):
        agents = await self.agent_service.find_all_by_project(project)

        if agent_code == AgentCode.Master:
            return await self._create_master_agent(agents, user_id, session_id, debug_mode, file_ids)
        else:
            if agents:
                agent_config = self._find_agent_config(agents, agent_code)
            else:
                agent_config = []
            return await self._create_worker_agent(agent_code, agent_config, user_id, session_id, debug_mode, file_ids)

    @staticmethod
    def _find_agent_config(agents_data: List, agent_id: AgentCode):
        for agent in agents_data:
            if getattr(agent, "code", "") == agent_id.value:  # Use 'code' field
                return agent
        raise ValueError(f"No configuration found for agent: {agent_id}")

    async def _create_master_agent(self, all_agents_data, user_id, session_id, debug_mode, file_ids):
        return MasterAgent(
            agent_id=AgentCode.Master,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            all_agents_config=all_agents_data,  # ALL agents configs (including master)
            file_ids=file_ids,
        ).agent

    async def _create_worker_agent(self, agent_id, agent_config, user_id, session_id, debug_mode, file_ids):

        # Get agent class
        agent_class = self.AGENT_CLASSES[agent_id]

        return agent_class(
            agent_id=agent_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            agent_config=agent_config,
            file_ids=file_ids,
        ).agent
