from typing import Union

from agno.embedder.base import Embedder

from app.config.base_config import AzureOpenAIConfig


def create_azure_embedding_model(embedding_config: AzureOpenAIConfig) -> Embedder:
    from agno.embedder.azure_openai import AzureOpenAIEmbedder

    return AzureOpenAIEmbedder(
        api_key=embedding_config.api_key.get_secret_value(),
        azure_endpoint=embedding_config.azure_endpoint,
        api_version=embedding_config.api_version,
        **(embedding_config.kwargs if embedding_config.kwargs else {}),
    )


def create_embedding_model(embedding_config: Union[AzureOpenAIConfig]) -> Embedder:
    """Connect to the configured text encoder using AGNO."""
    match embedding_config:
        case AzureOpenAIConfig():
            return create_azure_embedding_model(embedding_config)
        case _:
            raise ValueError(f"Unsupported embedding provider: {type(embedding_config)}")
