from typing import Optional, Union

from agno.models.azure import AzureOpenAI
from agno.models.base import Model

from app.config import APP_CONFIG
from app.config.base_config import AzureOpenAIConfig


def create_azure_chat_model(chat_config: AzureOpenAIConfig, model_id: str) -> AzureOpenAI:
    """Create an Azure OpenAI chat model using AGNO."""
    api_key = chat_config.api_key.get_secret_value()
    params = {
        "id": model_id,
        "azure_endpoint": chat_config.azure_endpoint,
        "api_version": chat_config.api_version,
        **(chat_config.kwargs if chat_config.kwargs else {}),
    }
    if api_key:
        params["api_key"] = api_key
    return AzureOpenAI(**params)


def create_chat_model(
    chat_config: Union[AzureOpenAIConfig] = APP_CONFIG.chat_model_config, model_id: Optional[str] = None
) -> Model:
    """Connect to the configured chat model (Azure, OpenAI, Claude, etc.) using AGNO, classified by model_id."""
    if model_id:
        model_id_lower = model_id.lower()
        if "gpt" in model_id_lower or "azure" in model_id_lower:
            return create_azure_chat_model(chat_config, model_id)
        else:
            raise ValueError(f"Unsupported or unknown model_id: {model_id}")
    else:
        raise ValueError("model_id must be provided to create a chat model.")
