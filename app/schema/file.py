from datetime import datetime
from typing import Optional

from pydantic import Field

from pkg.base import BaseModel


class FileSchema(BaseModel):
    id: str = Field(..., description="The id of the file")
    name: Optional[str] = Field(None, description="The original filename")
    size: Optional[int] = Field(None, description="The size of the file in bytes")
    content_type: Optional[str] = Field(None, description="The MIME type of the file")
    user_id: Optional[str] = Field(None, description="The user id who uploaded the file")
    status: Optional[str] = Field(None, description="The status of the file")
    error: Optional[str] = Field(None, description="The error message if the file is in error state")
    created_at: Optional[datetime] = Field(..., description="The timestamp when the file was created")
    updated_at: Optional[datetime] = Field(..., description="The timestamp when the file was last updated")
    conversation_id: Optional[str] = Field(None, description="The conversation id associated with the file")


class FileCreateRequest(BaseModel):
    name: str = <PERSON>(..., description="The original filename")
    content_type: str = Field(..., description="The MIME type of the file")
    size: int = Field(..., description="The size of the file in bytes", ge=0, le=10485760)  # 10 MB limit


class FileCreateResponse(FileSchema):
    presigned_url: str = Field(..., description="The pre-signed URL for uploading the file")
