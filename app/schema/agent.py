from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import Field, field_validator

from app.config import APP_CONFIG
from app.enum import AgentStatus
from pkg.base import BaseModel


class PromptTemplate(BaseModel):
    instructions: Optional[str] = Field(default=None, description="Primary instructions for the agent")
    additional_instruction: Optional[str] = Field(default=None, description="Optional extra context")


class AgentSchema(BaseModel):
    id: str = Field(..., description="Id of the agent")
    name: str = Field(..., description="Readable name of the agent")
    code: str = Field(..., description="Unique code for the agent")
    role: str = Field(..., description="Role or responsibility of the agent")
    description: Optional[str] = Field(default=None, description="Description of the agent's purpose")
    system_prompt: Optional[str] = Field(default=None, description="System prompt for the agent, can not be changed")
    additional_prompt: Optional[str] = Field(default=None, description="User configurable prompt")
    model_id: str = Field(
        default="gpt-4.1", description="Name of the Large Language Model (LLM) that this agent will use"
    )
    tools: List[str] = Field(default_factory=list, description="List of tools available to the agent")
    project: Optional[str] = Field(..., description="Project ID this agent belongs to")
    version: str = Field(..., description="Version number of the agent (major.minor format)")
    is_active: bool = Field(default=True, description="Whether this version is the active one")
    status: int = Field(..., description="Agent status as numeric value (0=Draft, 1=Submitted, 7=Approved)")
    created_at: Optional[datetime] = Field(default=None, description="Timestamp when this agent was created")
    updated_at: Optional[datetime] = Field(default=None, description="Timestamp when this agent was last updated")


class AgentProfileUpdateSchema(BaseModel):
    system_prompt: str = Field(..., description="System prompt for the agent")
    additional_prompt: Optional[str] = Field(default=None, description="User configurable prompt")
    model_id: str = Field(..., description="Name of the Large Language Model (LLM) that this agent will use")
    project: Optional[str] = Field(default=None, description="Project ID this agent belongs to")
    status: Optional[int] = Field(default=None, description="Agent status (0=Draft, 1=Submitted, 7=Approved)")

    @classmethod
    @field_validator("model_id")
    def validate_model_id(cls, value):
        if value not in APP_CONFIG.chat_model_config.model_ids:
            raise ValueError(f"Invalid model_id: {value}")
        return value
    
    @classmethod
    @field_validator("status")
    def validate_status(cls, value):
        if value is not None:
            try:
                AgentStatus.from_int(value)
            except ValueError:
                raise ValueError(f"Invalid status value: {value}. Valid values are: {[s.value for s in AgentStatus]}")
        return value


class AgentVersionListSchema(BaseModel):
    """Schema for listing agent versions with status information"""
    version: str = Field(..., description="Agent version")
    status: int = Field(..., description="Agent status as numeric value")
    is_active: bool = Field(..., description="Whether this version is active")
    created_at: Optional[datetime] = Field(default=None, description="When this version was created")
    updated_at: Optional[datetime] = Field(default=None, description="When this version was last modified")
    id: str = Field(..., description="Version ID")
    
    @classmethod
    def from_agent_model(cls, agent) -> 'AgentVersionListSchema':
        """Create AgentVersionListSchema from Agent model"""
        return cls(
            version=agent.version,
            status=agent.status.value,
            is_active=agent.is_active,
            created_at=agent.created_at,
            updated_at=agent.updated_at,
            id=str(agent.id)
        )
