from datetime import datetime

from pydantic import Field
from typing import Dict, Optional

from pkg.base import BaseModel


class TokenUsageFilter(BaseModel):
    start_date: Optional[datetime] = Field(None, description="Start date of the filter in ISO format")
    end_date: Optional[datetime] = Field(None, description="End date of the filter in ISO format")
    user_id: Optional[str] = Field(None, description="User id to filter by")
    project_id: Optional[str] = Field(None, description="Project id to filter by")


class TokenUsageResponse(BaseModel):
    total_conversations: int
    total_messages: int
    cost_per_1m_prompt_tokens: Dict[str, float] = Field({}, description="Cost per 1M prompt tokens by model")
    cost_per_1m_completion_tokens: Dict[str, float] = Field({}, description="Cost per 1M completion tokens by model")
    cost_per_1m_cached_tokens: Dict[str, float] = Field({}, description="Cost per 1M cached tokens by model")
    prompt_tokens: Dict[str, int] = Field({}, description="Prompt tokens usage by model")
    cached_prompt_tokens: Dict[str, int] = Field({}, description="Cached prompt tokens usage by model")
    completion_tokens: Dict[str, int] = Field({}, description="Completion tokens usage by model")
