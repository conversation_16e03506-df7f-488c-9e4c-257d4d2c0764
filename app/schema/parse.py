from typing import List, Literal, Optional, Dict, Any
from enum import Enum
from pydantic import Field

from pkg.base import BaseModel


class ArtifactParseRequest(BaseModel):
    user_request: str = Field(..., max_length=int(1e5), description="User request to parse for artefacts")
    content: str = Field(..., max_length=int(1e5), description="Content of the message to parse for artefacts")
    project_id: Optional[str] = Field(None, description="Project id of the message to parse for artefacts")
    context: Dict[str, Any] = Field({}, description="Context of the message to parse for artefacts")


class UserRequirement(BaseModel):
    name: str = Field(..., description="Name of the user requirement, if present in <context></context> use existing name")
    type: Literal[1, 2] = Field(
        ...,
        description="Type of the user requirement following mapping rules including 1 - Original, 2 - Change Request",
    )
    priority: Literal[1, 2, 3, 4] = Field(
        ...,
        description="Priority level of the user requirement following mapping rules including 1 - Low, 2 - Medium, 3 - High, 4 - Urgent",
    )
    description: str = Field(..., description="Detailed description of the user requirement")


class ComponentType(str, Enum):
    USER_INPUT = "User input"
    SINGLE_LINE_TEXT = "Single line of text"
    MULTIPLE_LINE_TEXT = "Multiple line of text"
    RICH_TEXT = "Rich text"
    SINGLE_CHOICE_DROPDOWN = "Single choice dropdown list"
    SINGLE_CHOICE_DROPDOWN_FILL = "Single choice dropdown list with Fill in"
    MULTIPLE_CHOICES_DROPDOWN = "Multiple choices dropdown list"
    MULTIPLE_CHOICES_DROPDOWN_FILL = "Multiple choices dropdown list with Fill in"
    USER_PICKER = "User picker"
    CHECK_BOX = "Check box"
    RADIO_BUTTON = "Radio button"
    DATE_ONLY = "Date time (Date only)"
    DATE_AND_TIME = "Date time (Date and time)"
    TIME_ONLY = "Date time (Time only)"
    NUMBER = "Number"
    LINK = "Link"
    IMAGE = "Image"
    ATTACHMENT = "Attachment"
    LABEL = "Label"
    BUTTON = "Button"
    ICON = "Icon"
    TABLE = "Table"
    COLUMN = "Column"
    COMPONENT_GROUP = "Component Group"
    CUSTOM = "Custom"


class ScreenComponent(BaseModel):
    component: str = Field(..., description="Name of the component")
    component_type: ComponentType
    description: str = Field(..., description="Detailed description of the component about purpose & function")
    editable: bool = Field(..., description="Whether the component is editable or not")
    mandatory: bool = Field(..., description="Whether the component is mandatory or not")
    default_value: Optional[str] = Field(None, description="Default value of the component if any")


class HLRArtefact(BaseModel):
    name: str
    description: Optional[str]


class HLRActor(HLRArtefact):
    name: str = Field(..., description="Name of the actor, if present in <context></context> use existing name")
    description: Optional[str] = Field(..., description="Description explaining its business purpose")


class HLRDataObject(HLRArtefact):
    name: str = Field(..., description="Name of the data object, if present in <context></context> use existing name")
    description: Optional[str] = Field(..., description="Description explaining its business purpose")


class HLRWorkflow(HLRArtefact):
    name: str = Field(..., description="Name of the workflow, if present in <context></context> use existing name")
    description: Optional[str] = Field(..., description="Description explaining its business purpose")


class ScreenSpecification(HLRArtefact):
    name: str = Field(..., description="Name of the screen, if present in <context></context> use existing name")
    description: Optional[str] = Field(..., description="Description explaining its business purpose")
    components: List[ScreenComponent] = Field(..., description="List of components in this screen")
    access: str = Field(..., description="Access control of the screen")
    objects: List[str] = Field([],
                               description="List of objects involved in this screen, only referenced in <context></context>")

    actors: List[str] = Field([],
                              description="List of actors involved in this screen, only referenced in <context></context>")
    use_cases: List[str] = Field([],
                                 description="List of use cases involved in this screen, only referenced in <context></context>")


class BusinessRule(BaseModel):
    step: int = Field(...,
                      description="Step number of the business rule, starting from 1, sub step is represented as 1.1, 1.2, etc.")
    name: str = Field(..., description="Name of the business rule")
    content: Optional[str] = Field(..., description="Detailed description of the business rule")


class UseCaseSpecification(HLRArtefact):
    name: str = Field(..., description="Name of the use case, if present in <context></context> use existing name")
    description: Optional[str] = Field(..., description="Description explaining its business purpose")
    actors: List[str] = Field([],
                              description="List of actors involved in this use case, only referenced in <context></context>")
    objects: List[str] = Field([],
                               description="List of objects involved in this use case, only referenced in <context></context>")
    pre_condition: str = Field(..., description="Precondition of the use case")
    trigger: str = Field(..., description="Trigger of the use case")
    post_condition: str = Field(..., description="Post condition of the use case")
    business_rules: List[BusinessRule] = Field(
        [],
        description="List of business rules involved in this use case"
    )


class ArtefactParsedData(BaseModel):
    actors: List[HLRActor] = Field(..., description="List of actors, usually defined in the Actors section if present")
    objects: List[HLRDataObject] = Field(
        ..., description="List of data objects, usually defined in the Data Objects section if present"
    )
    use_cases: List[UseCaseSpecification] = Field(
        ..., description="List of use cases/functions, usually defined in the Use Cases/Functions section if present"
    )
    workflows: List[HLRWorkflow] = Field(
        ..., description="List of workflows, usually defined in the Workflows section if present"
    )
    screens: List[ScreenSpecification] = Field(
        ..., description="List of screens, usually defined in the Screens section if present"
    )
    user_requirements: List[UserRequirement] = Field(
        ..., description="List of user requirements, usually defined in the User Requirement section if present"
    )
