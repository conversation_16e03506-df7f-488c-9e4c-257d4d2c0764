from typing import Optional

from pydantic import BaseModel, Field


class ChunkMetadata(BaseModel):
    """
    Metadata for a chunk of a document that has been split for processing.
    """

    user_id: str = Field(..., description="ID of the user who uploaded the document")
    document_type: str = Field(..., description="Document format type, e.g., md, pdf, docx, html, etc.")
    document_id: str = Field(..., description="ID of the original document")
    document_name: str = Field(..., description="Name of the original document")
    created_at: Optional[str] = Field(None, description="(Optional) Timestamp of indexing")
    source_file: Optional[str] = Field(None, description="(Optional) Original file path")
