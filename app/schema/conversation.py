from datetime import datetime
from typing import Optional

from pydantic import Field

from pkg.base import BaseModel


class ConversationSchema(BaseModel):
    id: str = Field(..., description="Id of the conversation")
    title: str = Field("", description="Title of the conversation, will be automatically set by first message")
    project_id: str = Field(..., description="Id of the project this conversation belongs to")
    user_id: str = Field(..., description="Id of the user who created this conversation")
    prompt_tokens: int = Field(0, description="Number of tokens used in the prompt")
    completion_tokens: int = Field(0, description="Number of tokens used in the completion")
    created_at: Optional[datetime] = Field(..., description="Timestamp when this conversation was created")
    updated_at: Optional[datetime] = Field(..., description="Timestamp when this conversation was last updated")


class ConversationCreateSchema(BaseModel):
    project_id: str = Field(..., description="Id of the project this conversation belongs to")
