#!/usr/bin/env python3
"""
Unified Agent Migration & Seed Script

This script performs all necessary migrations and seeding for agent data:
1. Fixes MongoDB indexes for versioning
2. Migrates existing agents to versioning system
3. Adds missing 'project' field (default: "Default")
4. Adds missing 'status' field (default: 0 = DRAFT)
5. Seeds sample agent data (with all required fields)
6. Verifies setup

Usage:
    docker compose exec fastapi-app uv run python app/migrations/seed/seed_agents_full_migration.py
    # Or with specific operations:
    docker compose exec fastapi-app uv run python app/migrations/seed/seed_agents_full_migration.py --fix-indexes --migrate-versions --add-project-field --add-status-field --seed-sample-data --verify
"""

import asyncio
import argparse
import os
import sys
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import OperationFailure, DuplicateKeyError

class AgentSeeder:
    def __init__(self):
        self.mongodb_url = os.getenv("MONGODB_URL", "mongodb://mongodb:27017")
        self.database_name = os.getenv("DATABASE_NAME", "bavista")
        self.client = None
        self.db = None
        self.agents_collection = None

    async def connect(self):
        print(f"🔌 Connecting to MongoDB: {self.mongodb_url}")
        print(f"📊 Database: {self.database_name}")
        self.client = AsyncIOMotorClient(self.mongodb_url)
        self.db = self.client[self.database_name]
        self.agents_collection = self.db.agents
        try:
            await self.client.admin.command('ping')
            print("✅ MongoDB connection successful")
            return True
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False

    async def close(self):
        if self.client:
            self.client.close()
            print("🔌 MongoDB connection closed")

    async def fix_indexes(self):
        print("\n🔧 Fixing MongoDB indexes...")
        try:
            current_indexes = await self.agents_collection.list_indexes().to_list(length=None)
            index_names = [idx["name"] for idx in current_indexes]
            print("Current indexes:")
            for idx in current_indexes:
                index_type = "COMPOUND" if len(idx.get('key', {})) > 1 else "SINGLE"
                unique_str = "UNIQUE" if idx.get('unique', False) else "NON-UNIQUE"
                print(f"  - {idx['name']}: {idx.get('key', {})} ({index_type}, {unique_str})")
            if "code_1" in index_names:
                print("\n🗑️  Dropping old unique code index...")
                await self.agents_collection.drop_index("code_1")
                print("✅ Dropped old unique code index (code_1)")
            else:
                print("\nℹ️  Old unique code index (code_1) not found")
            compound_index_exists = any(
                idx.get("key") == {"code": 1, "version": 1} 
                for idx in current_indexes
            )
            if not compound_index_exists:
                print("\n🔧 Creating compound (code, version) unique index...")
                await self.agents_collection.create_index(
                    [("code", 1), ("version", 1)], 
                    unique=True,
                    name="code_version_unique"
                )
                print("✅ Created compound (code, version) unique index")
            else:
                print("\nℹ️  Compound (code, version) index already exists")
            return True
        except Exception as e:
            print(f"❌ Error fixing indexes: {e}")
            return False

    async def migrate_versions(self):
        print("\n📦 Migrating existing agents to versioning system...")
        try:
            agents_without_version = await self.agents_collection.count_documents({"version": {"$exists": False}})
            agents_with_datetime_version = await self.agents_collection.count_documents({"version": {"$type": "date"}})
            agents_with_zero_version = await self.agents_collection.count_documents({"version": "0.0"})
            print(f"📊 Migration summary:")
            print(f"  - Agents without version field: {agents_without_version}")
            print(f"  - Agents with datetime version: {agents_with_datetime_version}")
            print(f"  - Agents with version '0.0': {agents_with_zero_version}")
            updated_count = 0
            if agents_without_version > 0:
                result = await self.agents_collection.update_many(
                    {"version": {"$exists": False}},
                    {"$set": {"version": "0.1", "is_active": True}}
                )
                updated_count += result.modified_count
                print(f"✅ Updated {result.modified_count} agents without version field")
            if agents_with_datetime_version > 0:
                result = await self.agents_collection.update_many(
                    {"version": {"$type": "date"}},
                    {"$set": {"version": "0.1", "is_active": True}}
                )
                updated_count += result.modified_count
                print(f"✅ Updated {result.modified_count} agents with datetime version")
            if agents_with_zero_version > 0:
                result = await self.agents_collection.update_many(
                    {"version": "0.0"},
                    {"$set": {"version": "0.1"}}
                )
                updated_count += result.modified_count
                print(f"✅ Updated {result.modified_count} agents with version '0.0'")
            print("\n🔍 Checking for duplicate active versions...")
            pipeline = [
                {"$group": {"_id": "$code", "codes": {"$addToSet": "$code"}}},
                {"$project": {"_id": 0, "code": "$_id"}}
            ]
            agent_codes_cursor = self.agents_collection.aggregate(pipeline)
            agent_codes = [doc["code"] async for doc in agent_codes_cursor]
            fixed_duplicates = 0
            for agent_code in agent_codes:
                agent_versions = await self.agents_collection.find({"code": agent_code}).to_list(length=None)
                active_versions = [agent for agent in agent_versions if agent.get("is_active", False)]
                if len(active_versions) > 1:
                    def version_sort_key(agent):
                        version = agent.get("version", "0.1")
                        if isinstance(version, str):
                            try:
                                major, minor = version.split('.')
                                return (int(major), int(minor))
                            except (ValueError, IndexError):
                                return (0, 1)
                        return (0, 1)
                    sorted_versions = sorted(active_versions, key=version_sort_key, reverse=True)
                    for i, agent in enumerate(sorted_versions):
                        if i > 0:
                            await self.agents_collection.update_one(
                                {"_id": agent["_id"]},
                                {"$set": {"is_active": False}}
                            )
                            fixed_duplicates += 1
                elif len(active_versions) == 0:
                    if agent_versions:
                        sorted_versions = sorted(agent_versions, key=lambda x: x.get("version", "0.1"), reverse=True)
                        await self.agents_collection.update_one(
                            {"_id": sorted_versions[0]["_id"]},
                            {"$set": {"is_active": True}}
                        )
                        fixed_duplicates += 1
            print(f"✅ Fixed {fixed_duplicates} duplicate active version issues")
            return {
                "agents_updated_with_version": updated_count,
                "duplicate_active_versions_fixed": fixed_duplicates
            }
        except Exception as e:
            print(f"❌ Error during migration: {e}")
            return None

    async def add_project_field(self):
        print("\n🔧 Adding project field to existing agents...")
        try:
            result = await self.agents_collection.update_many(
                {"$or": [
                    {"project": {"$exists": False}},
                    {"project": None},
                    {"project": ""}
                ]},
                {"$set": {"project": "Default", "updated_at": datetime.now()}}
            )
            print(f"✅ Updated {result.modified_count} agents with project='Default'")
            return result.modified_count
        except Exception as e:
            print(f"❌ Error adding project field: {e}")
            return 0

    async def add_status_field(self):
        print("\n🔧 Adding status field to existing agents...")
        try:
            result = await self.agents_collection.update_many(
                {"status": {"$exists": False}},
                {"$set": {"status": 0}}  # AgentStatus.DRAFT = 0
            )
            print(f"✅ Updated {result.modified_count} agents with status=0 (DRAFT)")
            return result.modified_count
        except Exception as e:
            print(f"❌ Error adding status field: {e}")
            return 0

    async def seed_sample_data(self):
        print("\n🌱 Seeding sample agent data...")
        from bson import ObjectId
        now = datetime.now()
        
        # Clear existing seed data first
        print("🗑️  Removing previous seed data...")
        deleted_result = await self.agents_collection.delete_many({
            "code": {"$in": ["hlr-agent", "ur-agent", "master-agent"]}
        })
        print(f"✅ Removed {deleted_result.deleted_count} existing seed agents")
        
        # Default project agents (original 3)
        default_agents = [
            {
                "id": str(ObjectId()),
                "name": "HLR Agent",
                "code": "hlr-agent",
                "role": "reasoner",
                "description": "High-Level Reasoning agent for complex problem-solving tasks",
                "system_prompt": "You are a high-level reasoning agent. Analyze complex problems and provide structured solutions.",
                "additional_prompt": "",
                "model_id": "gpt-4",
                "tools": ["search", "calculator"],
                "project": "Default",
                "version": "0.1",
                "is_active": True,
                "status": 0,
                "parent_id": None,
                "created_at": now,
                "updated_at": now
            },
            {
                "id": str(ObjectId()),
                "name": "UR Agent",
                "code": "ur-agent",
                "role": "user_request",
                "description": "User Request handling agent for processing user interactions",
                "system_prompt": "You are a user request agent. Process user requests and provide helpful responses.",
                "additional_prompt": "",
                "model_id": "gpt-3.5-turbo",
                "tools": ["faq", "form"],
                "project": "Default",
                "version": "0.1",
                "is_active": True,
                "status": 0,
                "parent_id": None,
                "created_at": now,
                "updated_at": now
            },
            {
                "id": str(ObjectId()),
                "name": "Master Agent",
                "code": "master-agent",
                "role": "orchestrator",
                "description": "Master orchestration agent for coordinating other agents",
                "system_prompt": "You are a master agent. Coordinate and orchestrate other agents to complete complex tasks.",
                "additional_prompt": "",
                "model_id": "gpt-4",
                "tools": ["workflow", "delegation"],
                "project": "Default",
                "version": "0.1",
                "is_active": True,
                "status": 0,
                "parent_id": None,
                "created_at": now,
                "updated_at": now
            }
        ]
        
        # RTMVP project agents (new 3 with same structure)
        rtmvp_agents = [
            {
                "id": str(ObjectId()),
                "name": "HLR Agent",
                "code": "hlr-agent",
                "role": "reasoner",
                "description": "High-Level Reasoning agent for complex problem-solving tasks",
                "system_prompt": "You are a high-level reasoning agent. Analyze complex problems and provide structured solutions.",
                "additional_prompt": "",
                "model_id": "gpt-4",
                "tools": ["search", "calculator"],
                "project": "RTMVP",
                "version": "0.1",
                "is_active": True,
                "status": 0,
                "parent_id": None,
                "created_at": now,
                "updated_at": now
            },
            {
                "id": str(ObjectId()),
                "name": "UR Agent",
                "code": "ur-agent",
                "role": "user_request",
                "description": "User Request handling agent for processing user interactions",
                "system_prompt": "You are a user request agent. Process user requests and provide helpful responses.",
                "additional_prompt": "",
                "model_id": "gpt-3.5-turbo",
                "tools": ["faq", "form"],
                "project": "RTMVP",
                "version": "0.1",
                "is_active": True,
                "status": 0,
                "parent_id": None,
                "created_at": now,
                "updated_at": now
            },
            {
                "id": str(ObjectId()),
                "name": "Master Agent",
                "code": "master-agent",
                "role": "orchestrator",
                "description": "Master orchestration agent for coordinating other agents",
                "system_prompt": "You are a master agent. Coordinate and orchestrate other agents to complete complex tasks.",
                "additional_prompt": "",
                "model_id": "gpt-4",
                "tools": ["workflow", "delegation"],
                "project": "RTMVP",
                "version": "0.1",
                "is_active": True,
                "status": 0,
                "parent_id": None,
                "created_at": now,
                "updated_at": now
            }
        ]
        
        # Combine all agents
        sample_agents = default_agents + rtmvp_agents
        
        async def upsert_agent(agent_data):
            try:
                await self.agents_collection.insert_one(agent_data)
                print(f"✅ Inserted agent: {agent_data['code']} v{agent_data['version']} (project: {agent_data['project']})")
                return "inserted"
            except DuplicateKeyError:
                print(f"⚠️  Agent {agent_data['code']} v{agent_data['version']} (project: {agent_data['project']}) already exists")
                return "duplicate"
            except Exception as e:
                print(f"❌ Error seeding agent {agent_data['code']}: {e}")
                return "error"
        
        results = await asyncio.gather(*(upsert_agent(agent) for agent in sample_agents))
        
        print(f"\n📊 Seeding results:")
        print(f"  - Total agents processed: {len(sample_agents)}")
        print(f"  - Default project agents: {len(default_agents)}")
        print(f"  - RTMVP project agents: {len(rtmvp_agents)}")
        print(f"  - Inserted: {results.count('inserted')} agents")
        print(f"  - Duplicates: {results.count('duplicate')} agents")
        print(f"  - Errors: {results.count('error')} agents")
        
        return {
            "inserted": results.count('inserted'),
            "updated": 0,  # We're not updating, only inserting fresh
            "duplicate": results.count('duplicate'),
            "error": results.count('error')
        }

    async def verify_setup(self):
        print("\n🔍 Verifying setup...")
        try:
            total_agents = await self.agents_collection.count_documents({})
            active_agents = await self.agents_collection.count_documents({"is_active": True})
            default_agents = await self.agents_collection.count_documents({"project": "Default"})
            rtmvp_agents = await self.agents_collection.count_documents({"project": "RTMVP"})
            
            print(f"📊 Agent statistics:")
            print(f"  - Total agents: {total_agents}")
            print(f"  - Active agents: {active_agents}")
            print(f"  - Default project agents: {default_agents}")
            print(f"  - RTMVP project agents: {rtmvp_agents}")
            
            print("\n📋 Default project agents:")
            default_agents_cursor = self.agents_collection.find({"project": "Default", "is_active": True})
            async for agent in default_agents_cursor:
                print(f"  - {agent['code']} v{agent['version']}: {agent['name']}")
            
            print("\n📋 RTMVP project agents:")
            rtmvp_agents_cursor = self.agents_collection.find({"project": "RTMVP", "is_active": True})
            async for agent in rtmvp_agents_cursor:
                print(f"  - {agent['code']} v{agent['version']}: {agent['name']}")
            
            indexes = await self.agents_collection.list_indexes().to_list(length=None)
            compound_index_exists = any(
                idx.get("key") == {"code": 1, "version": 1} 
                for idx in indexes
            )
            if compound_index_exists:
                print("✅ Compound (code, version) index is correctly configured")
            else:
                print("❌ Compound index is missing!")
            return True
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False

async def main():
    parser = argparse.ArgumentParser(description="Unified Agent Migration & Seed Script")
    parser.add_argument("--fix-indexes", action="store_true", help="Fix MongoDB indexes")
    parser.add_argument("--migrate-versions", action="store_true", help="Migrate existing agents to versioning")
    parser.add_argument("--add-project-field", action="store_true", help="Add project field to agents")
    parser.add_argument("--add-status-field", action="store_true", help="Add status field to agents")
    parser.add_argument("--seed-sample-data", action="store_true", help="Seed sample agent data")
    parser.add_argument("--verify", action="store_true", help="Verify setup")
    args = parser.parse_args()
    run_all = not any([
        args.fix_indexes, args.migrate_versions, args.add_project_field, args.add_status_field, args.seed_sample_data, args.verify
    ])
    print("🚀 Starting Unified Agent Migration & Seed Script")
    print("=" * 50)
    seeder = AgentSeeder()
    try:
        if not await seeder.connect():
            print("❌ Failed to connect to MongoDB")
            sys.exit(1)
        success = True
        if run_all or args.fix_indexes:
            if not await seeder.fix_indexes():
                success = False
        if run_all or args.migrate_versions:
            result = await seeder.migrate_versions()
            if result is None:
                success = False
        if run_all or args.add_project_field:
            updated = await seeder.add_project_field()
            if updated == 0:
                print("ℹ️  No agents needed project field migration.")
        if run_all or args.add_status_field:
            updated = await seeder.add_status_field()
            if updated == 0:
                print("ℹ️  No agents needed status field migration.")
        if run_all or args.seed_sample_data:
            result = await seeder.seed_sample_data()
            if result is None:
                success = False
        if run_all or args.verify:
            if not await seeder.verify_setup():
                success = False
        if success:
            print("\n🎉 All operations completed successfully!")
            print("\n📝 Next steps:")
            print("   1. Test your API endpoints")
            print("   2. Verify agent versioning works correctly") 
            print("   3. Check that PATCH requests create new versions")
            print("   4. Use GET /v1/agents to see only active versions")
        else:
            print("\n⚠️  Some operations failed. Check the logs above.")
    except Exception as e:
        print(f"❌ Script failed with error: {e}")
        sys.exit(1)
    finally:
        await seeder.close()

if __name__ == "__main__":
    asyncio.run(main())
