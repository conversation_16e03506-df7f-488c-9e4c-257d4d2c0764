"""
Migration script to add project field to existing agents.
This script adds project="Default" to all existing agents that don't have the field.
"""

import asyncio
from datetime import datetime

from app.model import Agent


async def add_project_field_to_existing_agents():
    """
    Add project="Default" to existing agents that don't have project field.
    This migration ensures all existing agents belong to the "Default" project.
    """
    print("Starting migration: Adding project field to existing agents...")
    
    try:
        # Find all agents that don't have project field or have null/empty project
        agents_to_update = await Agent.find(
            {
                "$or": [
                    {"project": {"$exists": False}},
                    {"project": None},
                    {"project": ""}
                ]
            }
        ).to_list()
        
        if not agents_to_update:
            print("No agents found that need project field migration.")
            return
        
        print(f"Found {len(agents_to_update)} agents that need project field added.")
        
        # Update all agents to have project="Default"
        updated_count = 0
        for agent in agents_to_update:
            await agent.update({"$set": {"project": "Default", "updated_at": datetime.now()}})
            updated_count += 1
            
        print(f"Successfully updated {updated_count} agents with project='Default'")
        print("Migration completed successfully!")
        
    except Exception as e:
        print(f"Error during migration: {e}")
        raise


async def main():
    """Main function to run the migration"""
    from app.db.mongo import connect_db
    
    # Connect to database
    await connect_db()
    
    # Run migration
    await add_project_field_to_existing_agents()


if __name__ == "__main__":
    asyncio.run(main())
