"""
External API client for communicating with ORCA backend system.
This module handles HTTP requests to external services with JWT authentication.
"""

import urllib.parse
import httpx
from typing import Dict, List, Optional, Any
from fastapi import HTTPException
from datetime import datetime

from app.config import APP_CONFIG
from pkg.base.model import BaseModel


class ProjectInfo(BaseModel):
    """Project information from external API"""
    project_id: int
    project_name: str
    project_code: str
    default_paging: int
    methodology: str
    roles: List[str]
    last_access: datetime


class UserInfo(BaseModel):
    """User information from external API"""
    user_name: str
    full_name: str
    email: str
    projects: List[ProjectInfo]
    role: int


class ExternalApiClient:
    """Client for making authenticated requests to external ORCA backend"""
    
    def __init__(self):
        self.base_url = APP_CONFIG.external_api_config.orca_backend_url
        self.timeout = 30.0
    
    async def get_user_info(self, jwt_token: str) -> UserInfo:
        """
        Get user information from external ORCA backend
        
        Args:
            jwt_token: JWT token for authentication
            
        Returns:
            UserInfo: User information including projects and roles
            
        Raises:
            HTTPException: If request fails or user is unauthorized
        """
        headers = {
            "Authorization": f"Bearer {jwt_token}",
            "Content-Type": "application/json"
        }
        
        url = f"{self.base_url}/api/Auth/Me"
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 401:
                    raise HTTPException(status_code=401, detail="Unauthorized - Invalid JWT token")
                elif response.status_code == 403:
                    raise HTTPException(status_code=403, detail="Forbidden - Access denied")
                elif response.status_code != 200:
                    raise HTTPException(
                        status_code=response.status_code, 
                        detail=f"External API error: {response.text}"
                    )
                
                data = response.json()
                
                # Parse projects
                projects = [
                    ProjectInfo(
                        project_id=proj["projectId"],
                        project_name=proj["projectName"], 
                        project_code=proj["projectCode"],
                        default_paging=proj["defaultPaging"],
                        methodology=proj["methodology"],
                        roles=proj["roles"],
                        last_access=datetime.fromisoformat(proj["lastAccess"].replace("Z", "+00:00"))
                    )
                    for proj in data.get("projects", [])
                ]
                
                return UserInfo(
                    user_name=data["userName"],
                    full_name=data["fullName"],
                    email=data["email"],
                    projects=projects,
                    role=data["role"]
                )
                
            except httpx.TimeoutException:
                raise HTTPException(status_code=504, detail="External API timeout")
            except httpx.RequestError as e:
                raise HTTPException(status_code=503, detail=f"External API connection error: {str(e)}")
            except ValueError as e:
                raise HTTPException(status_code=502, detail=f"Invalid response from external API: {str(e)}")
    
    async def validate_project_access(self, jwt_token: str, project_code: str) -> bool:
        """
        Validate if user has access to a specific project
        
        Args:
            jwt_token: JWT token for authentication
            project_code: URL-encoded project code to check access for
            
        Returns:
            bool: True if user has access, False otherwise
        """
        try:
            # Decode the URL-encoded project code
            decoded_project_code = urllib.parse.unquote(project_code)
            
            user_info = await self.get_user_info(jwt_token)
            return any(
                project.project_code.lower() == decoded_project_code.lower() 
                for project in user_info.projects
            )
        except HTTPException:
            return False
    
    async def get_user_project_roles(self, jwt_token: str, project_code: str) -> Optional[List[str]]:
        """
        Get user roles for a specific project
        
        Args:
            jwt_token: JWT token for authentication
            project_code: URL-encoded project code to get roles for
            
        Returns:
            Optional[List[str]]: List of roles if user has access, None otherwise
        """
        try:
            # Decode the URL-encoded project code
            decoded_project_code = urllib.parse.unquote(project_code)
            
            user_info = await self.get_user_info(jwt_token)
            for project in user_info.projects:
                if project.project_code.lower() == decoded_project_code.lower():
                    return project.roles
            return None
        except HTTPException:
            return None
