import async<PERSON>
from logging import Logger
from typing import Optional

from bson import ObjectId

from app.constant import FileStatus
from app.model.file import File
from app.schema import FileCreateRequest
from pkg.base import Pageable
from pkg.exception import HttpException
from pkg.message import ErrorMessage
from pkg.service.external import AzureBlobService

logger = Logger(__name__)


class FileService:
    def __init__(self):
        self.blob_service = AzureBlobService()

    @staticmethod
    def get_blob_key(user_id: str, file_id: str, file_name: str) -> str:
        return f"uploads/{user_id}/{file_id}/{file_name}"

    @staticmethod
    async def get_files_by_user_id(user_id: str, pageable: Pageable):
        query = File.find(File.user_id == user_id)
        _files = (
            await query.limit(pageable.limit)
            .skip(pageable.offset)
            .sort((pageable.sort_by, pageable.sort_order))
            .to_list()
        )
        total = await query.count()
        return _files, total

    @staticmethod
    async def create_file(payload: FileCreateRequest, user_id: str) -> File:
        file = File(
            user_id=user_id,
            name=payload.name,
            size=payload.size,
            content_type=payload.content_type,
        )
        await File.save(file)
        return file

    @staticmethod
    async def get_by_id(file_id: str, user_id: Optional[str] = None) -> File:
        file = await File.find_one(File.id == ObjectId(file_id), File.user_id == user_id if user_id else None)
        if not file:
            raise HttpException.not_found(ErrorMessage.FILE_NOT_EXISTS)

        return file

    async def delete_file(self, file_id: str, user_id: Optional[str] = None):
        file = await self.get_by_id(file_id, user_id)

        # Delete from blob storage
        if file.key:
            await self.blob_service.delete_blob(file.key)

        # Delete knowledge base from vectordb
        try:
            from app.preprocessing import DocumentProcessor

            processor = DocumentProcessor()
            await processor.delete_document(file.user_id, file_id)
        except Exception as e:
            logger.error(f"Failed to delete knowledge base for file {file_id}: {e}")

        # Delete file record
        await File.delete(file)

    async def commit_file_uploaded(self, file_id: str, user_id: Optional[str] = None) -> File:
        file = await self.get_by_id(file_id, user_id)
        if not file.status == FileStatus.created:
            raise HttpException.bad_request(ErrorMessage.FILE_NOT_IN_CREATED_STATUS)
        is_file_uploaded = await self.blob_service.check_blob_exists(file.key)
        if not is_file_uploaded:
            raise HttpException.bad_request(ErrorMessage.FILE_NOT_UPLOADED)
        file.status = FileStatus.uploaded

        async def process_file_async():
            from app.preprocessing import DocumentProcessor

            file.status = FileStatus.processing
            await File.save(file)

            try:
                # Check if file type is supported
                processor = DocumentProcessor()

                try:
                    # Process document into vectordb using agno
                    processed_key = await processor.process_document(
                        file_id=file_id,
                        user_id=file.user_id,
                        file_key=file.key,
                        file_name=file.name,
                    )
                    file.processed_key = processed_key
                except Exception as e:
                    file.status = FileStatus.error
                    file.error = str(e)
                    logger.error(f"Failed to process file {file_id}: {e}")
                    await File.save(file)
                    return

                file.status = FileStatus.processed
                logger.info(f"Successfully processed file {file_id}")

            except Exception as e:
                file.status = FileStatus.error
                file.error = str(e)
                logger.error(f"Failed to process file {file_id}: {e}")

            await File.save(file)

        asyncio.create_task(process_file_async())

        await File.save(file)
        return file

    async def get_presigned_url_for_upload(self, file: File) -> str:
        if not file.key:
            file.key = self.get_blob_key(file.user_id, str(file.id), file.name)
            await File.save(file)

        return await self.blob_service.generate_presigned_url(
            blob_name=file.key,
            permission=self.blob_service.get_uploadable_permission(),
            content_type=file.content_type,
            size=file.size,
        )

    async def get_presigned_url_for_view(self, user_id: str, file_id: str, download: bool = False) -> str:
        file = await self.get_by_id(file_id, user_id)

        return await self.blob_service.generate_presigned_url(
            blob_name=file.key, file_name=file.name, download=download
        )
