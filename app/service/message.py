import asyncio
from logging import Logger
from typing import Any, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Union

from agno.agent import Agent
from agno.models.response import ToolExecution
from agno.team import Team, TeamRunEvent, TeamRunResponse, TeamRunResponseEvent
from beanie import SortDirection
from bson import ObjectId
from fastapi import Depends

from app.constant import StreamMessageEvent
from app.model import Message
from app.schema import MessageEvent, MessageEventError, SendMessageRequest
from pkg.base.pageable import Pageable
from pkg.exception import HttpException
from pkg.message import ErrorMessage
from pkg.response import SSEResponse

from .statistic import StatisticService
from .agno import AgnoService
from .conversation import ConversationService
from .file import FileService

logger = Logger(__name__)


class MessageService:
    def __init__(
            self,
            conversation_service: ConversationService = Depends(ConversationService),
            statistic_service: StatisticService = Depends(StatisticService),
            agno_service: AgnoService = Depends(AgnoService),
            file_service: FileService = Depends(FileService),
    ):
        self.conversation_service = conversation_service
        self.agno_service = agno_service
        self.file_service = file_service
        self.statistic_service = statistic_service
        self.num_chunks_to_hold = 20
        self.__message_chunk_counter: Dict[str, int] = {}
        self.__message_holder: Dict[str, str] = {}

    @staticmethod
    async def get_by_id(message_id: str, conversation_id: str = None):
        query_filters = [Message.id == ObjectId(message_id)]
        if conversation_id is not None:
            query_filters.append(Message.conversation_id == conversation_id)

        message = await Message.find_one(*query_filters)
        if not message:
            raise HttpException.not_found(ErrorMessage.MESSAGE_NOT_EXISTS)
        return message

    @staticmethod
    async def get_conversation_messages(conversation_id: str, pageable: Pageable):
        query = Message.find(Message.conversation_id == conversation_id)
        _messages = (
            await query.limit(pageable.limit).skip(pageable.offset).sort(("_id", SortDirection.DESCENDING)).to_list()
        )
        total = await query.count()
        return reversed(_messages), total

    async def update_by_id(self, message_id: str, update_payload: dict):
        message = await self.get_by_id(message_id)
        return await message.update({"$set": update_payload})

    async def update_content_by_id(self, message_id, content: str):
        message = await self.update_by_id(message_id, {"content": content})
        return message

    async def update_message_content_with_session_sync(self, message_id: str, conversation_id: str, content: str):
        """
        Complete business logic for updating message content including validation and session sync.

        Args:
            message_id: ID of the message to update
            conversation_id: ID of the conversation
            content: New content for the message

        Returns:
            Updated message object

        Raises:
            HttpException: If message not found or user tries to edit their own message
        """
        # Get the message to check if it's an AI message
        message = await self.get_by_id(message_id, conversation_id)

        # Only allow editing AI messages, not user messages
        if not message.is_bot:
            raise HttpException.bad_request("Users are not allowed to edit their own messages")

        # Update message content in database
        updated_message = await self.update_content_by_id(message_id, content)

        # Update the session state for AI messages
        await self.agno_service.update_session_message_content(
            conversation_id=conversation_id,
            message_id=message_id,
            content=content,
            run_id=message.user_id if message.is_bot else None,
        )

        return updated_message

    @staticmethod
    async def create(conversation_id: str, user_id: str, content: str, is_bot: bool):
        message = Message(conversation_id=conversation_id, user_id=user_id, content=content, is_bot=is_bot)
        await Message.save(message)
        return message

    """
    !NOTE: This method is deprecated, use send_message_see instead
    """

    async def send_message(
            self, agent: Union[Agent, Team], conversation_id: str, user_id: str, payload: SendMessageRequest
    ):
        content = payload.content
        await self.conversation_service.generate_conversation_name(conversation_id, content)

        user_message = await self.create(conversation_id, user_id, content, False)
        message = user_message.content
        if payload.references:
            message += f"\nAttached: {[f'Name: {ref.name}' + (f'\n{ref.content}' if ref.content else '') for ref in payload.references]}"
        response: TeamRunResponse = await agent.arun(message=message, user_id=user_id, session_id=conversation_id)
        bot_message = await self.create(
            conversation_id, response.run_id, response.content, True
        )  # Store run_id in user_id for bot messages
        bot_message.reply_to = str(user_message.id)
        self.__map_message_from_agent_response(bot_message, response)
        await Message.save(bot_message)
        await self.statistic_service.update_token_usage(bot_message, response)
        return bot_message

    async def send_message_see(
            self, agent: Union[Agent, Team], conversation_id: str, user_id: str, payload: SendMessageRequest
    ) -> AsyncGenerator[str, None]:
        content = payload.content
        bot_message = None
        try:
            # Create async task for conversation name generation
            conversation_name_task = asyncio.create_task(
                self.conversation_service.generate_conversation_name(conversation_id, content)
            )

            user_message = await self.create(conversation_id, user_id, content, False)
            user_message.references = payload.references
            asyncio.create_task(Message.save(user_message))
            yield SSEResponse.generate(
                MessageEvent(**user_message.serialize(), event=StreamMessageEvent.UserMessageCreated).model_dump(
                    by_alias=True
                ))
            message = user_message.content
            if payload.references:
                message += f"\nAttached: {[f'Name: {ref.name}' + (f'\n{ref.content}' if ref.content else '') for ref in payload.references]}"
            response = await agent.arun(
                message=message,
                user_id=user_id,
                session_id=conversation_id,
                stream=True,
                stream_intermediate_steps=True,
            )
            bot_message = await self.create(conversation_id, "", "", True)
            bot_message.reply_to = str(user_message.id)

            # Track if conversation name has been yielded
            conversation_name_yielded = False

            async for response_chunk in response:
                # Check if conversation name task is done and yield result
                if not conversation_name_yielded and conversation_name_task.done():
                    conversation_name = await conversation_name_task
                    if conversation_name:
                        yield SSEResponse.generate(
                            MessageEvent(
                                conversation_id=conversation_id,
                                event=StreamMessageEvent.ConversationNameGenerated,
                                conversation_name=conversation_name,
                            )
                        )
                    conversation_name_yielded = True

                chunk = await self.handle_agent_event(bot_message, response_chunk)
                if chunk:
                    yield SSEResponse.generate(chunk)
                    asyncio.create_task(Message.save(bot_message))
            self.__map_message_from_agent_response(bot_message, agent.run_response)
            yield SSEResponse.generate(
                MessageEvent(**bot_message.serialize(), event=StreamMessageEvent.MessageComplete).model_dump(
                    by_alias=True
                )
            )
            # Ensure conversation name is yielded even if agent response is quick
            if not conversation_name_yielded:
                conversation_name = await conversation_name_task
                if conversation_name:
                    yield SSEResponse.generate(
                        MessageEvent(
                            conversation_id=conversation_id,
                            event=StreamMessageEvent.ConversationNameGenerated,
                            conversation_name=conversation_name,
                        )
                    )
            asyncio.create_task(Message.save(bot_message))
            asyncio.create_task(self.statistic_service.update_token_usage(bot_message, agent.run_response))
        except Exception as e:
            yield SSEResponse.generate(
                MessageEventError(
                    event=StreamMessageEvent.MessageError,
                    error=str(e),
                    id=str(bot_message.id) if bot_message else None,
                    conversation_id=conversation_id,
                ).model_dump(by_alias=True)
            )
            raise
        return

    async def handle_agent_event(self, message: Message, event: TeamRunResponseEvent) -> MessageEvent | None:
        response_event = MessageEvent(
            id=str(message.id), event=StreamMessageEvent.MessageDelta, conversation_id=message.conversation_id
        )
        match event.event:
            case TeamRunEvent.run_started:
                message.user_id = event.run_id  # Store run_id in user_id for bot messages
                response_event = MessageEvent(**message.serialize(), event=StreamMessageEvent.MessageCreated)

            case TeamRunEvent.run_response_content:
                current_chunk_count = self.__message_chunk_counter.get(response_event.id, 0)

                if current_chunk_count < self.num_chunks_to_hold:
                    self.__message_holder[response_event.id] = (
                            self.__message_holder.get(response_event.id, "") + event.content
                    )
                    self.__message_chunk_counter[response_event.id] = current_chunk_count + 1
                    return None
                else:
                    response_event.content = self.__message_holder[response_event.id] + event.content
                    del self.__message_chunk_counter[response_event.id]
                    del self.__message_holder[response_event.id]
            case TeamRunEvent.tool_call_started:
                response_event.step = self.__map_tool_execution_to_step(event.tool)
                # If the step mapping fails, return None
                if not response_event.step:
                    return None
                response_event.step["done"] = False
            case TeamRunEvent.tool_call_completed:
                response_event.step = self.__map_tool_execution_to_step(event.tool)
                # If the step mapping fails, return None
                if not response_event.step:
                    return None
                message.steps.append(response_event.step)
            case _:
                # Return None for unhandled event types
                return None

        # Return the serialized response event as a dictionary
        return response_event

    def __map_message_from_agent_response(self, message: Message, response: TeamRunResponse):
        message.response_time = sum(response.metrics["time"])
        message.reasoning_content = response.reasoning_content
        message.content = response.content
        message.steps = (
            [self.__map_tool_execution_to_step(tool_execution) for tool_execution in response.tools]
            if response.tools
            else []
        )

    @staticmethod
    def __map_tool_execution_to_step(tool_execution: ToolExecution) -> Dict[str, Any]:
        return (
            {
                "id": tool_execution.tool_call_id,
                "name": tool_execution.tool_name,
                "args": tool_execution.tool_args,
                "result": tool_execution.result,
                "done": True,
            }
            if tool_execution
            else None
        )
