from datetime import datetime
from logging import Logger
from typing import Optional

from beanie import SortDirection
from bson import ObjectId
from fastapi import Depends

from app.model import Conversation, Message
from app.orchestrator.tools import generate_conversation_name
from app.service.agno import AgnoService
from pkg.base.pageable import Pageable
from pkg.exception import HttpException
from pkg.message import ErrorMessage

logger = Logger(__name__)


class ConversationService:
    def __init__(self, agno_service: AgnoService = Depends(AgnoService)):
        self.agno_service = agno_service

    async def get_conversations_by_project(self, project_id: str, user_id: str, pageable: Pageable):
        query = Conversation.find(Conversation.project_id == project_id, Conversation.user_id == user_id)
        conversations = (
            await query.limit(pageable.limit)
            .skip(pageable.offset)
            .sort((pageable.sort_by, pageable.sort_order))
            .to_list()
        )
        total = await query.count()

        return conversations, total

    @staticmethod
    async def create(project_id: str, user_id: str):
        conversation = Conversation(project_id=project_id, user_id=user_id, created_at=datetime.now())
        return await Conversation.insert_one(conversation)

    @staticmethod
    async def get_by_id(conversation_id: str):
        conversation = await Conversation.find_one(Conversation.id == ObjectId(conversation_id))
        if not conversation:
            raise HttpException.not_found(ErrorMessage.CONVERSATION_NOT_EXISTS)
        return conversation

    async def update_by_id(self, conversation_id: str, update_payload: dict):
        conversation = await self.get_by_id(conversation_id)
        await conversation.update({"$set": update_payload})
        return conversation

    async def delete_by_id(self, conversation_id: str):
        conversation = await self.get_by_id(conversation_id)
        await self.agno_service.delete_session(conversation_id)
        await Message.find(Message.conversation_id == conversation_id).delete()
        return await Conversation.delete(conversation)

    async def update_title_by_id(self, conversation_id: str, title: str):
        return await self.update_by_id(conversation_id, {Conversation.title: title})

    @staticmethod
    async def get_conversation_messages(conversation_id: str, pageable: Pageable):
        query = Message.find(Message.conversation_id == conversation_id)
        _messages = (
            await query.limit(pageable.limit)
            .skip(pageable.offset)
            .sort(("created_at", SortDirection.DESCENDING))
            .to_list()
        )
        total = await query.count()
        return _messages, total

    async def generate_conversation_name(self, conversation_id: str, first_message_content: str) -> Optional[str]:
        conversation = await self.get_by_id(conversation_id)
        # If conversation already has a title, return None
        if conversation.title:
            return None
        conversation.title = await generate_conversation_name(first_message_content)
        await Conversation.save(conversation)
        return conversation.title
