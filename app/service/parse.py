from logging import Logger

from app.orchestrator.tools import parse_data_from_message
from app.schema import ArtifactParseRequest
from app.schema.parse import ArtefactParsedData

logger = Logger(__name__)


class ParseService:
    def __init__(self):
        pass

    @staticmethod
    async def parse_artefact(request: ArtifactParseRequest) -> ArtefactParsedData:
        return await parse_data_from_message(request)
