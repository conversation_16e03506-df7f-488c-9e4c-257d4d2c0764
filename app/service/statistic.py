import asyncio
from logging import Logger

from app.config import APP_CONFIG
from app.model import Conversation, Message
from app.schema import To<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TokenUsageResponse, Count
from beanie.operators import In
from agno.team import TeamRunResponse
from bson import ObjectId

logger = Logger(__name__)


class StatisticService:
    def __init__(self):
        pass

    async def get_token_usage(self, query: TokenUsageFilter) -> TokenUsageResponse:
        conversation_filters: list = []
        message_filters: list = []

        if query.start_date:
            message_filters.append(Message.created_at >= query.start_date)

        if query.end_date:
            message_filters.append(Message.created_at <= query.end_date)

        if query.user_id:
            conversation_filters.append(Conversation.user_id == query.user_id)

        if query.project_id:
            conversation_filters.append(Conversation.project_id == query.project_id)

        conversations = await Conversation.find(*conversation_filters).to_list()
        message_filters.append(In(Message.conversation_id, [str(conversation.id) for conversation in conversations]))

        [total_messages, conversation_count,
         [total_prompt_tokens, total_completion_tokens, total_cached_tokens]] = await asyncio.gather(
            Message.find(Message.is_bot == False, *message_filters).count(),
            self.__get_distinct_conversation_count(message_filters),
            self.__get_total_tokens(message_filters)
        )

        return TokenUsageResponse(total_conversations=conversation_count if conversation_count else 0,
                                  total_messages=total_messages,
                                  cost_per_1m_prompt_tokens=APP_CONFIG.cost_per_1m_prompt_tokens,
                                  cost_per_1m_completion_tokens=APP_CONFIG.cost_per_1m_completion_tokens,
                                  cost_per_1m_cached_tokens=APP_CONFIG.cost_per_1m_cached_tokens,
                                  prompt_tokens=total_prompt_tokens,
                                  completion_tokens=total_completion_tokens,
                                  cached_prompt_tokens=total_cached_tokens
                                  )

    @staticmethod
    async def __get_distinct_conversation_count(message_filters: list) -> int:
        counts = await Message.find(*message_filters).aggregate([
            {"$group": {"_id": f"${Message.conversation_id}"}},
            {"$count": "total"}
        ], projection_model=Count).to_list()
        return counts[0].total if counts else 0

    @staticmethod
    async def __get_total_tokens(message_filters: list):
        list_of_token_usage = await Message.find(Message.is_bot == True, *message_filters).aggregate([
            {
                '$project': {
                    'prompt_tokens': {
                        '$objectToArray': '$detail_prompt_tokens'
                    },
                    'completion_tokens': {
                        '$objectToArray': '$detail_completion_tokens'
                    },
                    'cached_tokens': {
                        '$objectToArray': '$detail_cached_prompt_tokens'
                    }
                }
            }, {
                '$project': {
                    'tokens': {
                        '$concatArrays': [
                            {
                                '$map': {
                                    'input': '$prompt_tokens',
                                    'as': 'e',
                                    'in': {
                                        'k': '$$e.k',
                                        'v': '$$e.v',
                                        'type': 'prompt'
                                    }
                                }
                            }, {
                                '$map': {
                                    'input': '$completion_tokens',
                                    'as': 'e',
                                    'in': {
                                        'k': '$$e.k',
                                        'v': '$$e.v',
                                        'type': 'completion'
                                    }
                                }
                            }, {
                                '$map': {
                                    'input': '$cached_tokens',
                                    'as': 'e',
                                    'in': {
                                        'k': '$$e.k',
                                        'v': '$$e.v',
                                        'type': 'cached'
                                    }
                                }
                            }
                        ]
                    }
                }
            }, {
                '$unwind': '$tokens'
            }, {
                '$group': {
                    '_id': {
                        'type': '$tokens.type',
                        'model': '$tokens.k'
                    },
                    'total': {
                        '$sum': '$tokens.v'
                    }
                }
            }
        ]).to_list()

        total_prompt_tokens = {}
        total_completion_tokens = {}
        total_cached_tokens = {}

        for item in list_of_token_usage:
            if item["_id"]["type"] == "prompt":
                total_prompt_tokens[item["_id"]["model"]] = item["total"]
            if item["_id"]["type"] == "completion":
                total_completion_tokens[item["_id"]["model"]] = item["total"]
            if item["_id"]["type"] == "cached":
                total_cached_tokens[item["_id"]["model"]] = item["total"]

        return [total_prompt_tokens, total_completion_tokens, total_cached_tokens]

    async def update_token_usage(self, message: Message, response: TeamRunResponse) -> None:
        try:
            await self.update_message_token_usage(message, response)
            await self.update_conversation_token_usage(message)
        except Exception as e:
            logger.error(f"Failed to update token usage for message {message.id}: {e}")

    @staticmethod
    async def update_message_token_usage(message: Message, response: TeamRunResponse) -> None:
        try:
            message.detail_prompt_tokens = {
                response.model: sum(response.metrics["prompt_tokens"])
            }
            message.detail_completion_tokens = {
                response.model: sum(response.metrics["completion_tokens"])
            }
            message.detail_cached_prompt_tokens = {
                response.model: sum(response.metrics["cached_tokens"])
            }
            for member_response in response.member_responses:
                model = member_response.model
                message.detail_prompt_tokens[model] = message.detail_prompt_tokens.get(model, 0) + sum(
                    member_response.metrics["prompt_tokens"])
                message.detail_completion_tokens[model] = message.detail_completion_tokens.get(model, 0) + sum(
                    member_response.metrics["completion_tokens"])
                message.detail_cached_prompt_tokens[model] = message.detail_cached_prompt_tokens.get(model, 0) + sum(
                    member_response.metrics["cached_tokens"])
            message.prompt_tokens = sum(message.detail_prompt_tokens.values())
            message.completion_tokens = sum(message.detail_completion_tokens.values())
            message.cached_prompt_tokens = sum(message.detail_cached_prompt_tokens.values())
            await Message.save(message)
        except Exception as e:
            logger.error(f"Failed to update token usage for message {message.id}: {e}")

    @staticmethod
    async def update_conversation_token_usage(message: Message) -> None:
        try:
            await Conversation.find_one(Conversation.id == ObjectId(message.conversation_id)).update({
                "$inc": {
                    Conversation.prompt_tokens: message.prompt_tokens,
                    Conversation.completion_tokens: message.completion_tokens,
                    Conversation.cached_prompt_tokens: message.cached_prompt_tokens,
                }
            })
        except Exception as e:
            logger.error(f"Failed to sync token usage for conversation {message.conversation_id}: {e}")
            return
