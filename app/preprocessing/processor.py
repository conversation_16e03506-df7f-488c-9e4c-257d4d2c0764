from datetime import datetime
from logging import Lo<PERSON>
import os
import tempfile
from typing import Optional

from app.config import APP_CONFIG
from app.factories.embedding_factory import create_embedding_model
from app.factories.knowledge_base_factory import create_knowledge_base, create_vectordb
from app.schema.chunk_metadata import ChunkMetadata
from pkg.service.external import AzureBlobService

from .file_converter import FileConverter

logger = Logger(__name__)


class DocumentProcessor:
    """Document processor using agno chunking strategies and vectordb"""

    def __init__(self):
        self.embedder = create_embedding_model(APP_CONFIG.embedding_model_config)
        self.mongo_config = APP_CONFIG.mongo_config
        self.vectordb_config = APP_CONFIG.vectordb_config
        self.file_converter = FileConverter()
        self.blob_service = AzureBlobService()

    async def process_document(
        self,
        file_id: str,
        user_id: str,
        file_key: str,
        file_name: str,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None,
    ) -> str:
        """
        Process document content into vectordb using agno markdown workflow

        Workflow: File -> Convert to Markdown -> MarkdownChunking -> VectorDB

        Args:
            file_id: The file ID
            user_id: The user ID
            file_name: The file name
            file_key: The blob storage key for the file
            chunk_size: Maximum chunk size (uses config default if None)
            chunk_overlap: Overlap between chunks (uses config default if None)

        Raises:
            Exception: If processing fails
        """
        # Use configuration defaults if not provided
        if chunk_size is None:
            chunk_size = APP_CONFIG.knowledge_base_config.chunk_size
        if chunk_overlap is None:
            chunk_overlap = APP_CONFIG.knowledge_base_config.chunk_overlap

        if not self.is_supported(file_name):
            raise ValueError(f"Unsupported file type. Supported types: {self.get_supported_extensions()}")

        # Download file content from blob storage
        temp_file_path = await self.download_file(file_key)

        # Convert file content to markdown format using FileConverter
        markdown_file_path = self.file_converter.convert_to_markdown(file_name, temp_file_path)

        processed_blob_name = self.get_blob_key(user_id, file_id, file_name)

        # Upload converted markdown file back to blob storage
        await self.blob_service.upload_blob(processed_blob_name, markdown_file_path)

        try:
            # Create MarkdownKnowledgeBase using factory
            knowledge_base = create_knowledge_base()

            # Prepare metadata for documents
            additional_metadata = ChunkMetadata(
                user_id=user_id,
                document_type=os.path.splitext(file_name)[1][1:],
                document_id=file_id,
                document_name=file_name,
                created_at=str(datetime.now()),
            ).model_dump()

            # Load documents into vectordb with metadata
            await knowledge_base.aload_document(
                path=markdown_file_path, metadata=additional_metadata, recreate=False, skip_existing=False, upsert=False
            )

            logger.info(f"Successfully processed document {file_id} (user: {user_id})")
        except Exception as e:
            logger.error(f"Failed to process document {file_id} (user: {user_id}): {e}")
            raise e
        finally:
            # Clean up temporary file
            self.cleanup_temp_file(temp_file_path)
            self.cleanup_temp_file(markdown_file_path)

        return processed_blob_name

    async def download_file(self, file_key: str) -> str:
        """Download file from blob storage and return path to temporary file"""
        # Download file content from blob storage
        file_content = await self.blob_service.get_blob(file_key)
        if not file_content:
            raise ValueError("Failed to download file from blob storage")

        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        return temp_file_path

    def cleanup_temp_file(self, file_path: str) -> None:
        """
        Clean up temporary file

        Args:
            file_path: Path to the temporary file to clean up
        """
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            logger.warning(f"Failed to cleanup temp file {file_path}: {e}")

    def get_blob_key(self, user_id: str, file_id: str, file_path: str) -> str:
        dir_name, file_name = os.path.split(file_path)
        name, _ = os.path.splitext(file_name)
        new_file_name = f"processed_{name}.md"
        file_name = os.path.join(dir_name, new_file_name)
        return f"uploads/{user_id}/{file_id}/{file_name}"

    async def delete_document(self, user_id: str, file_id: str) -> bool:
        """Delete specific document from vectordb collection"""
        try:
            # Create vectordb directly
            vector_db = create_vectordb()

            collection = vector_db._get_collection()
            result = collection.delete_many({"user_id": user_id, "file_id": file_id})
            # Consider any deletion (even 0) as success
            success = result.deleted_count >= 0
            return success
        except Exception as e:
            logger.error(f"Failed to delete document {file_id}: {e}")
            return False

    def is_supported(self, file_name: str) -> bool:
        """Check if file type is supported"""
        return self.file_converter.is_supported(file_name)

    def get_supported_extensions(self) -> list[str]:
        """Get supported file extensions"""
        return self.file_converter.get_supported_extensions()
