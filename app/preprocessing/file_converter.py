from logging import Logger
import os
import tempfile

from app.config.get_config import get_config
from app.factories.chat_factory import create_chat_model

logger = Logger(__name__)


class FileConverter:
    """
    Simple file converter to handle conversion of various file types to markdown

    Currently supports: .md, .markdown files
    Future TODO: Add support for .txt, .pdf, .docx, .html, etc.
    """

    def __init__(self):
        # Currently enabled file types
        self.enabled_extensions = [
            ".md",
            ".markdown",
            ".pptx",
            ".docx",
            ".xlsx",
            ".xls",
            ".pdf",
            ".png",
            ".jpg",
            ".jpeg",
        ]

        # TODO: Add more file types in the future
        # self.future_extensions = ['.txt', '.pdf', '.docx', '.html', '.rtf']

    def convert_to_markdown(self, file_name: str, file_path: str) -> str:
        """
        Convert file content to markdown format and save to temporary file

        Args:
            file_name: Name of the file
            content: File content

        Returns:
            Path to the temporary markdown file
        """
        file_extension = self.get_file_extension(file_name)

        # Handle markdown files (already in markdown format)
        if file_extension in [".md", ".markdown"]:
            with open(file_path, "r", encoding="utf-8") as file:
                content = file.read()
            markdown_content = self._process_markdown_file(content)
        elif file_extension in [".pptx", ".docx", ".xlsx", ".xls", ".pdf"]:
            markdown_content = self._convert_office_to_markdown(file_path, file_extension)
        elif file_extension in [".png", ".jpg", ".jpeg"]:
            markdown_content = self._convert_image_to_markdown(file_path, file_extension)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")

        # Create temporary markdown file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False, encoding="utf-8") as tmp_file:
            tmp_file.write(markdown_content)
            tmp_file_path = tmp_file.name

        return tmp_file_path

    def _process_markdown_file(self, content: str) -> str:
        """
        Process markdown file (currently just cleaning)

        Args:
            content: Markdown content

        Returns:
            Cleaned markdown content
        """
        # Simple cleaning for markdown files
        lines = content.split("\n")
        cleaned_lines = []

        for line in lines:
            # Strip trailing whitespace
            cleaned_line = line.rstrip()
            cleaned_lines.append(cleaned_line)

        # Join lines and ensure proper formatting
        cleaned_content = "\n".join(cleaned_lines)

        # Remove excessive blank lines
        while "\n\n\n\n" in cleaned_content:
            cleaned_content = cleaned_content.replace("\n\n\n\n", "\n\n\n")

        # Ensure content ends with newline
        if cleaned_content and not cleaned_content.endswith("\n"):
            cleaned_content += "\n"

        return cleaned_content

    def _convert_office_to_markdown(self, file_path: str, file_extension: str) -> str:
        """Convert office documents to markdown"""
        from markitdown import MarkItDown

        md = MarkItDown(enable_plugins=False)
        result = md.convert_local(file_path, file_extension=file_extension)
        return result.text_content

    def _convert_image_to_markdown(self, file_path: str, file_extension: str) -> str:
        """Convert image to markdown"""
        from markitdown import MarkItDown

        config = get_config().chat_model_config
        config.azure_deployment = "gpt-4o"
        md = MarkItDown(llm_client=create_chat_model(config, model_id="gpt-4o").get_client(), llm_model="gpt-4o")
        result = md.convert_local(
            file_path,
            file_extension=file_extension,
            llm_prompt="Given an image, explain what is shown in the image in clear detail.",
        )
        return result.text_content

    def is_supported(self, file_name: str) -> bool:
        """
        Check if file type is supported

        Args:
            file_name: Name of the file

        Returns:
            True if file type is supported
        """
        extension = self.get_file_extension(file_name)
        return extension in self.enabled_extensions

    def get_supported_extensions(self) -> list[str]:
        """
        Get list of supported file extensions

        Returns:
            List of supported file extensions
        """
        return self.enabled_extensions.copy()

    @staticmethod
    def get_file_extension(file_name: str) -> str:
        """Extract file extension from filename"""
        if "." not in file_name:
            return ""
        _, ext = os.path.splitext(file_name)
        return ext
