from enum import Enum
from logging import getLogger
from typing import List, Optional

from fastapi import APIRouter, Depends, status, Request, Query

from app.schema import AgentProfileUpdateSchema, AgentSchema, AgentVersionListSchema
from app.service import AgentService
from app.auth.project_auth import validate_project_access_from_path
from app.service.external_api import UserInfo
from pkg.base import Pagination
from pkg.response import HttpResponse

logger = getLogger(__name__)

agents_router = APIRouter(prefix="/agents", tags=["Agents"])


@agents_router.get("", status_code=status.HTTP_200_OK, response_model=HttpResponse.PaginatedResponse[AgentSchema])
async def get_all_agents(project: Optional[str] = Query(None, description="Project code to filter agents by"),
                         agent_service: AgentService = Depends(AgentService)):
    agents = await agent_service.find_all_by_project(project)
    return Pagination([AgentSchema(**agent.serialize()) for agent in agents]).to_response()


@agents_router.get("/{agent_code}", response_model=AgentSchema, status_code=status.HTTP_200_OK)
async def get_agent_details(agent_code: str,
                            project: Optional[str] = Query(None, description="Project code to filter agents by"),
                            version: Optional[str] = Query(None, description="Version of the agent to retrieve"),
                            agent_service: AgentService = Depends(AgentService)):
    print(f"Getting agent {agent_code} version {version} in project {project}")
    agent = await agent_service.find_by_code(agent_code, version, project)
    return AgentSchema(**agent.serialize()).to_response()


@agents_router.get("/{agent_code}/versions", response_model=HttpResponse.Response[list[AgentVersionListSchema]],
                   status_code=status.HTTP_200_OK)
async def get_agent_versions(agent_code: str, agent_service: AgentService = Depends(AgentService)):
    agents = await agent_service.find_all_versions_by_code(agent_code)
    return [AgentVersionListSchema(**agent.serialize()) for agent in agents]


@agents_router.patch("/{agent_code}", response_model=HttpResponse.Response[AgentSchema], status_code=status.HTTP_200_OK)
async def update_agent_profile(
        agent_code: str, body: AgentProfileUpdateSchema, agent_service: AgentService = Depends(AgentService)
):
    agent = await agent_service.update_by_code(agent_code, body.model_dump(exclude_none=True))
    return AgentSchema(**agent.serialize()).to_response()
