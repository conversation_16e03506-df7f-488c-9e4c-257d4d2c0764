from logging import Logger

from fastapi import APIRouter, Depends, status

from app.schema import ArtefactParsedData
from app.schema.parse import ArtifactParseRequest
from app.service import ParseService
from pkg.response import HttpResponse

logger = Logger(__name__)

tool_router = APIRouter(prefix="/tools", tags=["AI Tools"])


@tool_router.post(
    "/artifact-parser", status_code=status.HTTP_200_OK, response_model=HttpResponse.Response[ArtefactParsedData]
)
async def parse_ai_message(payload: ArtifactParseRequest, parse_service: ParseService = Depends(ParseService)):
    result = await parse_service.parse_artefact(payload)
    return result.to_response()
