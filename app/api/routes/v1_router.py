from fastapi import APIRouter, Depends

from .agents import agents_router
from .conversations import conversations_router
from .files import files_router
from .playground import playground_router
from .tool import tool_router
from .statistic import statistic_router
from app.auth import jwt

v1_router = APIRouter(prefix="/v1", dependencies=[Depends(jwt)])
v1_router.include_router(agents_router)
v1_router.include_router(conversations_router)
v1_router.include_router(tool_router)
v1_router.include_router(files_router)
v1_router.include_router(playground_router)
v1_router.include_router(statistic_router)
