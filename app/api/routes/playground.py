from agno.playground import Playground

from app.orchestrator.agents.hlr_agent import H<PERSON><PERSON>gent
from app.orchestrator.agents.master_agent import MasterAgent
from app.orchestrator.agents.ur_agent import URAgent
from app.orchestrator.agents.usecase_agent import UseCaseAgent
from app.orchestrator.agents.screen_agent import ScreenAgent

######################################################
# Routes for the Playground Interface
######################################################

# Get Agents to serve in the playground
hlr_agent = HLRAgent().agent
ur_agent = URAgent().agent
usecase_agent = UseCaseAgent().agent
screen_agent = ScreenAgent().agent
master_agent = MasterAgent().agent

# Create a playground instance
playground = Playground(agents=[hlr_agent, ur_agent, usecase_agent, screen_agent], teams=[master_agent])

# Get the router for the playground
playground_router = playground.get_async_router()
