from logging import Logger

from fastapi import APIRouter, Depends, Query, status
from fastapi.responses import RedirectResponse

from app.auth import get_user_id
from app.schema import FileCreateRequest
from app.schema.file import FileCreateResponse, FileSchema
from app.service.file import FileService
from pkg.base import Pageable, Pagination
from pkg.base.pageable import SortOrder
from pkg.response import HttpResponse

logger = Logger(__name__)

files_router = APIRouter(prefix="/files", tags=["Files"])


@files_router.get("", status_code=status.HTTP_200_OK, response_model=HttpResponse.PaginatedResponse[FileSchema])
async def get_user_files(
    user_id: str = Depends(get_user_id),
    file_service: FileService = Depends(FileService),
    limit: int = Query(20, description="Number of files to return"),
    offset: int = Query(0, description="Offset for pagination"),
    sort_by: str = Query("updated_at", description="Field to sort by"),
    sort_order: SortOrder = Query(SortOrder.desc, description="Sort order (asc or desc)"),
):
    files, count = await file_service.get_files_by_user_id(
        user_id, Pageable(limit=limit, offset=offset, sort_by=sort_by, sort_order=sort_order)
    )
    return Pagination([FileSchema(**file.serialize()) for file in files], count, limit, offset).to_response()


@files_router.post(
    "",
    status_code=status.HTTP_201_CREATED,
    description="Create a new file",
    response_model=HttpResponse.Response[FileCreateResponse],
)
async def create_file(
    payload: FileCreateRequest, user_id: str = Depends(get_user_id), file_service: FileService = Depends(FileService)
):
    file = await file_service.create_file(payload, user_id)
    presigned_url = await file_service.get_presigned_url_for_upload(file)
    return FileCreateResponse(**file.serialize(), presigned_url=presigned_url).to_response(
        status_code=status.HTTP_201_CREATED
    )


@files_router.get("/{file_id}", status_code=status.HTTP_200_OK, response_model=HttpResponse.Response[FileSchema])
async def get_file_metadata(
    file_id: str, user_id: str = Depends(get_user_id), file_service: FileService = Depends(FileService)
):
    file = await file_service.get_by_id(file_id, user_id)
    return FileSchema(**file.serialize()).to_response()


@files_router.post(
    "/{file_id}/commit", status_code=status.HTTP_200_OK, response_model=HttpResponse.Response[FileSchema]
)
async def commit_file_uploaded(
    file_id: str, user_id: str = Depends(get_user_id), file_service: FileService = Depends(FileService)
):
    await file_service.commit_file_uploaded(file_id, user_id)
    return HttpResponse.ok()


@files_router.get("/{file_id}/view", status_code=status.HTTP_302_FOUND)
async def get_file_content(
    file_id: str,
    download: bool = Query(False, description="Whether to download the file instead of viewing it"),
    redirect: bool = Query(False, description="Whether to return the signed URL instead of redirecting"),
    user_id: str = Depends(get_user_id),
    file_service: FileService = Depends(FileService),
):
    presigned_url = await file_service.get_presigned_url_for_view(user_id, file_id, download)
    if not redirect:
        return HttpResponse.of(data=presigned_url)
    return RedirectResponse(presigned_url, status_code=status.HTTP_302_FOUND)


@files_router.delete("/{file_id}", status_code=status.HTTP_200_OK, response_model=HttpResponse.Response)
async def delete_file(
    file_id: str, user_id: str = Depends(get_user_id), file_service: FileService = Depends(FileService)
):
    await file_service.delete_file(file_id, user_id)
    return HttpResponse.ok()
