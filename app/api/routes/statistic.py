from fastapi import Depends, APIRouter
from logging import Logger

from app.schema import TokenUsageFilter
from app.service import StatisticService

logger = Logger(__name__)

statistic_router = APIRouter(prefix="/statistics", tags=["Statistics"])


@statistic_router.post("/token-usage")
async def get_token_usage(
    payload: TokenUsageFilter,
    statistic_service: StatisticService = Depends(StatisticService),
):
    # TODO: Check permission, if not admin, only allow to filter by project_id
    statistic = await statistic_service.get_token_usage(payload)
    return statistic.to_response()