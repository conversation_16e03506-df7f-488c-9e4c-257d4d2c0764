from enum import Enum


class StreamMessageEvent(str, Enum):
    ConversationNameGenerated = ("ConversationNameGenerated",)
    MessageCreated = ("MessageCreated",)
    MessageDelta = ("MessageDelta",)
    MessageError = ("MessageError",)
    MessageComplete = ("MessageComplete",)
    UserMessageCreated = ("UserMessageCreated",)


class ReferenceType(str, Enum):
    File = ("File",)
    LegacyFile = ("LegacyFile",)
    Actor = ("Actor",)
    MeetingMinute = ("MeetingMinute",)
    UserRequirement = ("UserRequirement",)
    Workflow = ("Workflow",)
    UseCase = ("UseCase",)
    Object = ("Object",)
    Screen = ("Screen",)
    Literal = ("Literal",)
