from typing import Any, Dict, Optional

from .base import Base


class AgnoSession(Base):
    class Settings:
        name = "agno_sessions"

    session_id: Optional[str]
    user_id: Optional[str]
    extra_data: Optional[Dict[str, Any]] = None
    team_data: Optional[Dict[str, Any]] = None
    session_data: Optional[Dict[str, Any]] = None
    memory: Optional[Dict[str, Any]] = None
    team_id: Optional[str] = None
    team_session_id: Optional[str] = None
