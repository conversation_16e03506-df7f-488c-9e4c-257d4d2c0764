from beanie import Document, PydanticObjectId


class Base(Document):

    def serialize(self):
        def _serialize_dict(d):
            for k, v in d.items():
                if isinstance(v, dict):
                    _serialize_dict(v)
                elif isinstance(v, list):
                    for item in v:
                        if isinstance(item, dict):
                            _serialize_dict(item)
                elif isinstance(v, PydanticObjectId):
                    d[k] = str(v)
            return d

        serialized = self.model_dump()

        return _serialize_dict(serialized)
