from datetime import datetime
from typing import Optional

from beanie import Insert, <PERSON>lace, SaveC<PERSON>es, Update, before_event

from app.constant import FileStatus

from .base import Base


class File(Base):
    class Settings:
        name = "files"

    user_id: str
    name: str = None
    key: Optional[str] = None
    processed_key: Optional[str] = None
    size: Optional[int] = None
    content_type: Optional[str] = None
    status: str = FileStatus.created
    error: Optional[str] = None
    created_at: Optional[datetime] = datetime.now()
    updated_at: Optional[datetime] = datetime.now()

    @before_event(Insert)
    def set_created_at(self):
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    @before_event(Update, SaveChanges, Replace)
    def set_updated_at(self):
        self.updated_at = datetime.now()
