from datetime import datetime
from typing import Optional

from beanie import Insert, Replace, SaveChanges, Update, before_event

from .base import Base


class Conversation(Base):
    class Settings:
        name = "conversations"

    title: str = ""
    project_id: str
    user_id: str
    prompt_tokens: int = 0
    cached_prompt_tokens: int = 0
    completion_tokens: int = 0
    created_at: Optional[datetime] = datetime.now()
    updated_at: Optional[datetime] = datetime.now()

    @before_event(Insert)
    def set_created_at(self):
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    @before_event(Update, SaveChanges, Replace)
    def set_updated_at(self):
        self.updated_at = datetime.now()
