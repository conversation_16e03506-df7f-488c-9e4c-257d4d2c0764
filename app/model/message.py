from datetime import datetime
from typing import Any, Dict, Optional

from beanie import Insert, <PERSON>lace, SaveChang<PERSON>, Update, before_event

from .base import Base


class Message(Base):
    class Settings:
        name = "messages"

    content: str = ""
    reasoning_content: Optional[str] = ""
    is_bot: bool = False
    steps: list[Dict[str, Any]] = []
    references: list = []
    prompt_tokens: int = 0
    detail_prompt_tokens: Optional[Dict[str, int]] = None
    cached_prompt_tokens: int = 0
    detail_cached_prompt_tokens: Optional[Dict[str, int]] = None
    completion_tokens: int = 0
    detail_completion_tokens: Optional[Dict[str, int]] = None
    response_time: Optional[float] = None
    user_id: Optional[str] = None  # If is bot message, it'll set to run_id
    conversation_id: str
    reply_to: Optional[str] = None
    created_at: Optional[datetime] = datetime.now()
    updated_at: Optional[datetime] = datetime.now()

    @before_event(Insert)
    def set_created_at(self):
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    @before_event(Update, SaveChang<PERSON>, Replace)
    def set_updated_at(self):
        self.updated_at = datetime.now()
