#!/usr/bin/env python3
"""
MongoDB Index Fix Script

This script fixes the MongoDB indexes for the agents collection:
1. Drops the old unique code index (code_1) that conflicts with versioning
2. Creates a compound unique index on (code, version) to allow multiple versions per agent

Run this script from the project root directory:
python fix_indexes.py

Or with uv:
uv run python fix_indexes.py

Or inside Docker container:
docker-compose exec fastapi-app uv run python fix_indexes.py
"""

import asyncio
import os
import sys
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import OperationFailure


async def fix_agent_indexes():
    """
    Fix MongoDB indexes: drop old unique code index and ensure compound (code, version) index exists
    """
    # MongoDB connection settings (adjust these based on your environment)
    # Inside Docker container, use service name 'mongodb' instead of 'localhost'
    mongodb_url = os.getenv("MONGODB_URL", "mongodb://mongodb:27017")
    database_name = os.getenv("DATABASE_NAME", "bavista")
    
    print(f"🔌 Connecting to MongoDB: {mongodb_url}")
    print(f"📊 Database: {database_name}")
    
    # Create MongoDB client
    client = AsyncIOMotorClient(mongodb_url)
    db = client[database_name]
    agents_collection = db.agents
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✅ MongoDB connection successful")
        
        # Get current indexes
        print("\n🔍 Checking current indexes...")
        current_indexes = await agents_collection.list_indexes().to_list(length=None)
        
        print("Current indexes:")
        for idx in current_indexes:
            print(f"  - {idx['name']}: {idx.get('key', {})}")
        
        index_names = [idx["name"] for idx in current_indexes]
        
        # Drop the old unique code index if it exists
        if "code_1" in index_names:
            print("\n🗑️  Dropping old unique code index...")
            try:
                await agents_collection.drop_index("code_1")
                print("✅ Successfully dropped old unique code index (code_1)")
            except OperationFailure as e:
                print(f"⚠️  Warning: Could not drop code_1 index: {e}")
        else:
            print("\nℹ️  Old unique code index (code_1) not found - nothing to drop")
        
        # Check if compound index exists
        compound_index_exists = any(
            idx.get("key") == {"code": 1, "version": 1} 
            for idx in current_indexes
        )
        
        if not compound_index_exists:
            print("\n🔧 Creating compound (code, version) unique index...")
            try:
                await agents_collection.create_index(
                    [("code", 1), ("version", 1)], 
                    unique=True,
                    name="code_version_unique"
                )
                print("✅ Successfully created compound (code, version) unique index")
            except OperationFailure as e:
                print(f"❌ Error creating compound index: {e}")
                return False
        else:
            print("\nℹ️  Compound (code, version) index already exists")
        
        # Show final index state
        print("\n📋 Final index state:")
        final_indexes = await agents_collection.list_indexes().to_list(length=None)
        for idx in final_indexes:
            index_type = "COMPOUND" if len(idx.get('key', {})) > 1 else "SINGLE"
            unique_str = "UNIQUE" if idx.get('unique', False) else "NON-UNIQUE"
            print(f"  - {idx['name']}: {idx.get('key', {})} ({index_type}, {unique_str})")
        
        print("\n🎉 Index fix completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error connecting to MongoDB or fixing indexes: {e}")
        return False
    finally:
        # Close the connection
        client.close()
        print("\n🔌 MongoDB connection closed")


async def main():
    """Main function to run the index fix"""
    print("🚀 Starting MongoDB Index Fix Script")
    print("=" * 50)
    
    success = await fix_agent_indexes()
    
    if success:
        print("\n✅ Script completed successfully!")
        print("\n📝 Next steps:")
        print("   1. Test your PATCH requests to verify the versioning system works")
        print("   2. Check that agents can be updated without duplicate key errors")
        sys.exit(0)
    else:
        print("\n❌ Script failed!")
        print("\n🔧 Troubleshooting:")
        print("   1. Check MongoDB connection settings")
        print("   2. Ensure MongoDB is running")
        print("   3. Verify database permissions")
        sys.exit(1)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
